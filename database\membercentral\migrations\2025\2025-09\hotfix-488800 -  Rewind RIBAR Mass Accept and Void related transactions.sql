use membercentral
GO

declare @orgcode varchar(5), @siteCode varchar(5), @siteID int, @orgID int, @enteredByMemberID int, @transactionID int, @vidPool xml, 
	@tids xml, @performDelete int;

select @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();
set @performDelete = 1;
set @orgcode = 'RIBAR';
set @siteCode  ='RIBAR';
set @orgID = dbo.fn_getOrgIDfromOrgCode(@siteCode);
set @siteID  = dbo.fn_getSiteIDfromSiteCode(@siteCode);


declare @subscriberIDsToRewind TABLE (subscriberID int PRIMARY KEY, rootSubscriberID int, statusHistoryID int,oldStatusID int);
declare @transToVoid TABLE (transactionID int, siteID int);
declare @acceptedSubscribers TABLE (subscriberID int, rootSubscriberID int, statusHistoryID int, oldStatusID int);
declare @modifiedSubs TABLE (membernumber varchar(100), subscriberID int);


SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


insert into @acceptedSubscribers (subscriberID,rootSubscriberID,statusHistoryID, oldStatusID)
select sh.subscriberID, ss.rootSubscriberID, sh.statusHistoryID, sh.oldStatusID
from dbo.sub_statusHistory sh
inner join dbo.sub_subscribers ss 
    on ss.orgID=@orgID
    and sh.orgID=@orgID
    and ss.subscriberID = sh.subscriberID
	and ss.statusID in (6,1)
    and sh.updateDate between '9/2/2025 13:00' and '9/2/2025 15:00'
    and sh.statusID=5 and sh.oldStatusID=4
inner join sub_rateFrequencies rf 
    on rf.rfid = ss.RFID
    and rf.rateID in (20422,20426)
inner join dbo.sub_subscribers rootss 
    on rootss.orgID=@orgID
    and rootss.subscriberID = ss.rootsubscriberID
	and rootss.statusID in (6,1)
inner join dbo.sub_subscriptions as sub 
    on sub.orgID = @orgID
    and sub.subscriptionID = rootss.subscriptionID
inner join dbo.sub_types as st on st.siteID=@siteID and st.typeID = sub.typeID;



-- find any subs that has addons added later modified afterwards (root has addons that were not in original changeset)
insert into @modifiedSubs (memberNumber, subscriberID)
select m.memberNumber, ass.subscriberID
from @acceptedSubscribers ass
inner join sub_subscribers ss
    on ss.orgID = @orgID
    and ss.subscriberID=ass.subscriberID
    and ss.rootSubscriberID = ss.subscriberID
inner join sub_subscribers sstree 
    on sstree.orgID=@orgID
    and sstree.rootSubscriberID = ss.rootSubscriberID
inner join sub_statusHistory sh 
    on sh.orgID = @orgID 
    and sh.subscriberID = sstree.subscriberID
inner join sub_statuses st 
    on sh.statusID = st.statusID
    and st.statusCode in ('P','A','I')
inner join ams_members m
    on m.memberID = ss.memberID
    and m.orgID = @orgID
left outer join @acceptedSubscribers treeass
    on treeass.subscriberID = sstree.subscriberID
where treeass.subscriberID is null
group by m.memberNumber, ass.subscriberID


-- display modifed subs before deleting them
select 'skipped due to modifications', * from @modifiedSubs

delete ass
from @acceptedSubscribers ass
inner join @modifiedSubs ms
    on ms.subscriberID = ass.subscriberID

select @@ROWCOUNT as deletedRootofModified

-- delete any subscribers whose rootsubscriberID is no longer in table
delete ass
from @acceptedSubscribers ass
inner join sub_subscribers ss on ss.orgID = @orgID and ss.subscriberID=ass.subscriberID
left outer join @acceptedSubscribers rootass on rootass.subscriberID = ss.rootSubscriberID
where rootass.subscriberID is null;

select @@ROWCOUNT as deletedAddonsOfModified

-- load remaining subscribers into rewind table
insert into @subscriberIDsToRewind (subscriberID, rootSubscriberID, statusHistoryID, oldStatusID)
select ass.subscriberID, ass.rootSubscriberID, ass.statusHistoryID, ass.oldStatusID
from @acceptedSubscribers ass;

-- get all transactions related to these subscriptions
insert into @transToVoid(transactionID, siteID)
select t.transactionID, t.recordedOnSiteID 
from @subscriberIDsToRewind rewind
inner join dbo.tr_applications ta on ta.orgID=@orgID and ta.itemID = rewind.subscriberID
	and ta.itemType = 'Dues'
inner join tr_transactions t on t.recordedOnSiteID = @siteID and t.transactionID = ta.transactionID;

if @performDelete <> 1 BEGIN
	select count(*) as subcount
	from @subscriberIDsToRewind rewind


	select count(distinct rootSubscriberID) as treecount
	from @subscriberIDsToRewind rewind

	select mactive.membernumber, mactive.memberID, count(*)
	from @subscriberIDsToRewind rewind
	inner join sub_subscribers ss 
        on ss.orgID=@orgID 
        and ss.subscriberID = rewind.subscriberID
    inner join dbo.ams_members m 
        on m.orgID = @orgID 
        and m.memberID = ss.memberID
    inner join dbo.ams_members mactive 
        on mactive.orgID = @orgID 
        and m.activememberID = mactive.memberID
    group by mactive.membernumber, mactive.memberID;



	select mactive.membernumber, mactive.memberID,rewind.*, ss.*
	from @subscriberIDsToRewind rewind
	inner join sub_subscribers ss 
        on ss.orgID=@orgID 
        and ss.subscriberID = rewind.subscriberID
    inner join dbo.ams_members m 
        on m.orgID = @orgID 
        and m.memberID = ss.memberID
    inner join dbo.ams_members mactive 
        on mactive.orgID = @orgID 
        and m.activememberID = mactive.memberID;

    select sh.*
    from @subscriberIDsToRewind rewind
    inner join sub_statusHistory sh 
        on sh.orgID = @orgID 
        and sh.subscriberID = rewind.subscriberID
        and sh.statusHistoryID >= rewind.statusHistoryID
    order by sh.dateRecorded;

	select *
	from @transToVoid tv
	inner join tr_transactions t on t.transactionID = tv.transactionID;

    select *
    from @transToVoid  tv
    inner join tr_applications ta on ta.transactionID = tv.transactionID;
END

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

if @performDelete = 1 BEGIN

    update ta set ta.status='D'
    from @transToVoid tv
    inner join tr_applications ta on ta.transactionID = tv.transactionID;

    update ss 
	set ss.statusID = sh.oldStatusID
    from @subscriberIDsToRewind rewind
    inner join sub_subscribers ss on ss.orgID = @orgID and ss.subscriberID = rewind.subscriberID
    inner join sub_statusHistory sh on sh.orgID = @orgID and sh.statusHistoryID = rewind.statusHistoryID;

    delete sh
    from @subscriberIDsToRewind rewind
    inner join sub_statusHistory sh
        on sh.orgID = @orgID 
        and sh.subscriberID = rewind.subscriberID
        and sh.statusHistoryID >= rewind.statusHistoryID;



	-- VOID ALL sales and allocations TIED TO these subcriptions
	select @transactionID = min(transactionID) from @transToVoid;
	while @transactionID is not null BEGIN
		set @vidPool = null;
		set @tids = null;
		select @siteID = siteID from @transToVoid where transactionID = @transactionID;

		EXEC dbo.tr_voidTransaction @siteID, @enteredByMemberID, null, @transactionID, 1, @vidPool OUTPUT, @tids OUTPUT;

		select @transactionID = min(transactionID) from @transToVoid where transactionID > @transactionID;
	END

 
    -- reprocess groups for members.
    IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
        DROP TABLE #tblMCQRun;
    CREATE TABLE #tblMCQRun (orgID int, memberID int, conditionID int);
    INSERT INTO #tblMCQRun (orgID, memberID, conditionID) values (@orgID, null, null)
    EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroups';
    IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
        DROP TABLE #tblMCQRun;


END
GO
