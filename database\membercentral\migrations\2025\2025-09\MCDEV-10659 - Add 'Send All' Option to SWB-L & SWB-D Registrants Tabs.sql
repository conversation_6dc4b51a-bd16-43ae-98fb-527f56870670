USE membercentral
GO

ALTER PROC dbo.sw_emailEnrollments
@siteID int,
@swType varchar(4),
@enrollmentIDList varchar(max),
@messageToParse varchar(max),
@messageWrapper varchar(max),
@emailTagTypeID int,
@emailFromName varchar(200), 
@emailReplyTo varchar(200), 
@emailSubject varchar(200), 
@contentVersionID int,
@recordedByMemberID int,
@deliveryReportEmail varchar(200),
@sendOnDate datetime,
@markRecipientAsReady bit,
@consentListIDs varchar(max) = NULL

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @numRecipients int, @messageTypeID int, @messageStatusIDInserting int, @messageStatusIDQueued int,
		@sendingSiteResourceID int, @supportProviderEmail varchar(100), @supportProviderName varchar(100), 
		@defaultOrgIdentityID int, @messageID int, @rawcontent varchar(max), @fieldID int, @fieldName varchar(300),  
		@vwSQL varchar(max), @ParamDefinition nvarchar(100), @mcSQL nvarchar(max), @colList varchar(max), 
		@colDataType varchar(40), @fieldValueString varchar(200), @contentToParse varchar(max), @referenceType varchar(30), @referenceID int;
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL);

	IF OBJECT_ID('tempdb..#tblDepoMemberIDs') IS NOT NULL 
		DROP TABLE #tblDepoMemberIDs;
	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	CREATE TABLE #tblDepoMemberIDs (memberID int, orgID int, enrollmentID int);
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));
	CREATE TABLE #tmpRecipientsMID (enrollmentID int, seminarID int, memberID int, mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int);
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	select @orgID = s.orgID, @sendingSiteResourceID = st.siteResourceID, @defaultOrgIdentityID = s.defaultOrgIdentityID
	from dbo.sites as s
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'SeminarWebAdmin';

	IF @sendOnDate < getDate()
		set @sendOnDate = getDate();

	INSERT INTO #tblDepoMemberIDs (memberID, orgID, enrollmentID)
	select mActive.memberID, mActive.orgID, e.enrollmentID
	from seminarWeb.dbo.tblEnrollments as e
	inner join dbo.fn_intListToTable(isnull(@enrollmentIDList,''),',') as limite on limite.listitem = e.enrollmentID
	inner join seminarWeb.dbo.tblUsers u ON u.userID = e.userID
	inner join trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	inner join dbo.ams_networkProfiles as np on np.depoMemberDataID = d.depomemberdataID
	inner join dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID 
		and mnp.status = 'A'
	inner join dbo.sites as s on s.siteID = mnp.siteID 
	inner join dbo.ams_members as m on m.memberID = mnp.memberID 
	inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	inner join trialsmith.dbo.depoTLA as tla on tla.state = s.sitecode 
		and tla.isLiveOnNewPlatform = 1
	where np.status = 'A'
	and mActive.status = 'A'
		union
	select mActive.memberID, mActive.orgID, e.enrollmentID
	from seminarWeb.dbo.tblEnrollments as e
	inner join dbo.fn_intListToTable(isnull(@enrollmentIDList,''),',') as limite on limite.listitem = e.enrollmentID
	inner join seminarWeb.dbo.tblUsers u ON u.userID = e.userID
	inner join trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	inner join dbo.ams_members as m on m.memberID = d.MCmemberIDtemp
	inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	where mActive.status = 'A';

	insert into #tmpRecipientsMID (enrollmentID, seminarID, memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
	select distinct e.enrollmentID, s.seminarID, m.memberID, me.email, metag.emailTypeID
	from seminarWeb.dbo.tblEnrollments as e
	inner join #tblDepoMemberIDs as tmp on tmp.enrollmentID = e.enrollmentID
	inner join seminarWeb.dbo.tblSeminars as s ON s.seminarID = e.seminarID
	inner join dbo.ams_members as m on m.memberID = tmp.memberID
	inner join dbo.ams_memberEmails as me on me.orgID = @orgID
		and me.memberID = m.memberID
	inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
		and metag.memberID = me.memberID
		and metag.emailTypeID = me.emailTypeID
	inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagTypeID = @emailTagTypeID	
	where m.status = 'A'
	and len(me.Email) > 0;

	select @numRecipients = count(*) from #tmpRecipientsMID;

	IF @numRecipients = 0 
		RAISERROR('No recipients for message.',16,1);

	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILSWREG';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';

	select TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
	from dbo.networks as net
	inner join dbo.networkSites as ns on net.networkID = ns.networkID
	inner join dbo.sites as s on s.siteID = ns.siteID
	where s.siteID = @siteID 
	and ns.isLoginNetwork = 1;

	-- add any necessary metadata fields
	SET @contentToParse = @messageToParse + isnull(@emailSubject,'');

	declare @regexMergeCode varchar(40);
	select @regexMergeCode = regexMergeCode from dbo.fn_getServerSettings();
	
	insert into @metadataFields (fieldName)
	select distinct left([Text],300)
	from dbo.fn_RegexMatches(@contentToParse,@regexMergeCode);
		
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@contentToParse,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;
	
	IF OBJECT_ID('tempdb..##tmpEmailSWEnrollments') IS NOT NULL 
		DROP TABLE ##tmpEmailSWEnrollments;

	IF @colList is null
		select memberID as MCMemberID
		into ##tmpEmailSWEnrollments
		from #tmpRecipientsMID;
	ELSE BEGIN
		set @vwSQL = 'select m.memberID as MCMemberID, ' + @colList + ' 
			into ##tmpEmailSWEnrollments 
			from #tmpRecipientsMID as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
		EXEC(@vwSQL);
	END

	SELECT m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
		m.membernumber, m.firstname + ' ' + m.lastname as fullname, 
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, tmp.mc_emailBlast_email, tmp.mc_emailBlast_emailTypeID, vw.*
	INTO #tmpRecipients
	FROM #tmpRecipientsMID as tmp
	INNER JOIN dbo.ams_members as m on m.memberID = tmp.memberID
	LEFT OUTER JOIN ##tmpEmailSWEnrollments as vw on vw.MCMemberID = m.memberID
	INNER JOIN dbo.orgIdentities as i on i.orgIdentityID = @defaultOrgIdentityID
	INNER JOIN dbo.ams_states as s on s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID;

	IF OBJECT_ID('tempdb..##tmpEmailSWEnrollments') IS NOT NULL 
		DROP TABLE ##tmpEmailSWEnrollments;

	-- get tmpRecipients columns
	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	inner join tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');

	alter table #tmpRecipients add recipientID int;

	IF @swType = 'SWL'
		SET @referenceType = 'SeminarWebLive';
	IF @swType = 'SWOD'
		SET @referenceType = 'SeminarWebOnDemand';
	IF @swType = 'SWB'
		SET @referenceType = 'SeminarWebBundle';

	SELECT TOP 1 @referenceID = seminarID 
	FROM #tmpRecipientsMID;

	BEGIN TRAN;
		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
			@fromName=@emailFromName, @fromEmail=@supportProviderEmail, @replyToEmail=@emailReplyTo, @senderEmail='', 
			@subject=@emailSubject, @contentVersionID=@contentVersionID, @messageWrapper=@messageWrapper, 
			@referenceType=@referenceType, @referenceID=@referenceID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

		-- update deliveryReportEmail
		IF nullIf(@deliveryReportEmail,'') is not null
			update platformMail.dbo.email_messages
			set deliveryReportEmail = @deliveryReportEmail
			where messageID = @messageID;
	COMMIT TRAN;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select @messageID, memberID, getdate(), fullname, mc_emailBlast_email, @messageStatusIDInserting, 
		null, null, mc_emailBlast_emailTypeID, @siteID,@initialQueuePriority
	from #tmpRecipients;

	-- no need to support multiple recipientIDs per memberID, so simple join is sufficient
	update tmp
	set tmp.recipientID = r.recipientID
	from #tmpRecipients as tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.memberID = tmp.memberiD
		and r.messageID = @messageID;

	-- add metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields;

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1;

	-- add recipient metadata
	set @ParamDefinition = N'@messageID int, @fieldID int';		
	select @fieldID = min(fieldID) from @metadataFields;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, memberID, fieldValue = ' + @fieldValueString + ', recipientID
				from #tmpRecipients;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID;
	END

	-- mark recipients as queued
	if @markRecipientAsReady = 1 
		update mrh 
		set mrh.emailStatusID = @messageStatusIDQueued
		from platformMail.dbo.email_messages as m
		inner join platformMail.dbo.email_messageRecipientHistory as mrh on m.messageID = mrh.messageID
			and m.messageID = @messageID;


	-- return recipients
	select recipientID, memberID, membernumber, mc_emailBlast_email as recipientEmail, @messageID as messageID
	from #tmpRecipients;

	IF OBJECT_ID('tempdb..#tblDepoMemberIDs') IS NOT NULL 
		DROP TABLE #tblDepoMemberIDs;
	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
