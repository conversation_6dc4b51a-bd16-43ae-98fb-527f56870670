<cfcomponent output="false">

	<cffunction name="getCouponsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="operationMode" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCoupons" result="local.qryCouponsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpCouponsSearch') IS NOT NULL 
					DROP TABLE ##tmpCouponsSearch;
				IF OBJECT_ID('tempdb..##tmpCoupons') IS NOT NULL
					DROP TABLE ##tmpCoupons;
				CREATE TABLE ##tmpCouponsSearch (couponID int PRIMARY KEY);
				CREATE TABLE ##tmpCoupons (couponID int, couponCode varchar(15), siteResourceID int, startDate datetime, endDate datetime, 
					status char(1), redemptionCount int, maxOverallUsageCount int, maxMemberUsageCount int, pctOff decimal(5,2), 
					pctOffMaxOff decimal(18,2), amtOff decimal(18,2), savingsAmt decimal(18,2), row int);

				DECLARE @orgID int, @siteID int, @fCouponCode varchar(15), @fStatus char(1), @fAvailableFrom date, 
					@fAvailableTo datetime, @fCreatedFrom date, @fCreatedTo datetime, @posStart int, 
					@posStartPlusCount int, @totalCount int;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartPlusCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">;

				<cfif arguments.event.getTrimValue('fCouponCode','') NEQ ''>
					SET @fCouponCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fCouponCode')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fStatus','') NEQ ''>
					SET @fStatus = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fStatus')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fAvailableFrom','') NEQ ''>
					SET @fAvailableFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fAvailableFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fAvailableTo','') NEQ ''>
					SET @fAvailableTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fAvailableTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedFrom','') NEQ ''>
					SET @fCreatedFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fCreatedFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedTo','') NEQ ''>
					SET @fCreatedTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fCreatedTo')# 23:59:59.997">;
				</cfif>

				INSERT INTO ##tmpCouponsSearch (couponID)
				select couponID
				from dbo.tr_coupons
				where siteID = @siteID
				<cfif arguments.event.getValue('fStatus','') NEQ ''>
					and status = @fStatus
				<cfelse>
					and status <> 'D'
				</cfif>
				<cfif arguments.event.getValue('fCouponCode','') NEQ ''>
					and couponCode = @fCouponCode
				</cfif>
				<cfif arguments.event.getValue('fAvailableFrom','') NEQ '' and arguments.event.getValue('fAvailableTo','') NEQ ''>
					and startDate between @fAvailableFrom AND @fAvailableTo
				<cfelseif arguments.event.getValue('fAvailableFrom','') NEQ ''>
					and startDate >= @fAvailableFrom
				<cfelseif arguments.event.getValue('fAvailableTo','') NEQ ''>
					and endDate <= @fAvailableTo
				</cfif>
				<cfif arguments.event.getValue('fCreatedFrom','') NEQ '' and arguments.event.getValue('fCreatedTo','') NEQ ''>
					and dateAdded between @fCreatedFrom AND @fCreatedTo
				<cfelseif arguments.event.getValue('fCreatedFrom','') NEQ ''>
					and dateAdded >= @fCreatedFrom
				<cfelseif arguments.event.getValue('fCreatedTo','') NEQ ''>
					and dateAdded <= @fCreatedTo
				</cfif>
				<cfif arguments.event.getValue('fUsage','') eq "ev">
					and eventsXML is not null
				<cfelseif arguments.event.getValue('fUsage','') eq "sub">
					and subscriptionsXML is not null
				<cfelseif arguments.event.getValue('fUsage','') eq "sto">
					and storeXML is not null
				<cfelseif arguments.event.getValue('fUsage','') eq "sw">
					and seminarwebXML is not null
				</cfif>;

				INSERT INTO ##tmpCoupons (couponID, couponCode, siteResourceID, startDate, endDate, status, maxOverallUsageCount, 
					maxMemberUsageCount, pctOff, pctOffMaxOff, amtOff, redemptionCount, row)
				select c.couponID, c.couponCode, c.siteResourceID, c.startDate, c.endDate, c.status, c.maxOverallUsageCount, 
					c.maxMemberUsageCount, c.pctOff, c.pctOffMaxOff, c.amtOff, sum(isnull(td.redemptionCount,0)),
					ROW_NUMBER() OVER (ORDER BY c.couponCode #arguments.event.getValue('orderDir')#)
				from dbo.tr_coupons as c
				inner join ##tmpCouponsSearch as tmp ON tmp.couponID = c.couponID
				outer apply (
					select td.couponID, td.itemID, td.itemType, td.redemptionCount
					from dbo.tr_transactionDiscounts as td
					inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
					where td.orgID = @orgID
					and td.couponID = c.couponID
					and td.isActive = 1
				) td
				where c.siteID = @siteID
				group by c.couponID, c.couponCode, c.siteResourceID, c.startDate, c.endDate, c.status, c.maxOverallUsageCount, 
					c.maxMemberUsageCount, c.pctOff, c.pctOffMaxOff, c.amtOff;

				SET @totalCount = @@ROWCOUNT;

				<cfif arguments.operationMode is 'couponGrid'>
					DELETE FROM ##tmpCoupons
					WHERE NOT (row > @posStart AND row <= @posStartPlusCount);

					-- amount saved
					update tmp
					set tmp.savingsAmt = tmpAmt.couponAmt
					from ##tmpCoupons as tmp
					inner join (
						select td.couponID, sum(t.amount) as CouponAmt
						from dbo.tr_transactionDiscounts as td
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
						where td.orgID = @orgID
						and td.isActive = 1
						group by td.couponID
					) as tmpAmt on tmpAmt.couponID = tmp.couponID;

					SELECT tmp.couponID, tmp.couponCode, tmp.siteResourceID, tmp.startDate, tmp.endDate, tmp.status, 
						tmp.status, tmp.redemptionCount, tmp.maxOverallUsageCount, tmp.maxMemberUsageCount, tmp.pctOff, 
						tmp.pctOffMaxOff, tmp.amtOff, tmp.savingsAmt, 
						case when c.subscriptionsXML is not null then 'Subscriptions' else '' end as forSubscriptions,
						case when c.eventsXML is not null then 'Events' else '' end as forEvents,
						case when c.storeXML is not null then 'Store' else '' end as forStore,
						case when c.seminarwebXML is not null then 'SeminarWeb' else '' end as forSeminarweb,
						@totalCount AS totalCount
					FROM ##tmpCoupons as tmp
					INNER JOIN dbo.tr_coupons as c on c.siteID = @siteID and c.couponID = tmp.couponID
					ORDER BY tmp.row;
				<cfelse>
					SELECT couponID
					FROM ##tmpCoupons					
					ORDER BY row;
				</cfif>				

				IF OBJECT_ID('tempdb..##tmpCoupons') IS NOT NULL
					DROP TABLE ##tmpCoupons;
				IF OBJECT_ID('tempdb..##tmpCouponsSearch') IS NOT NULL 
					DROP TABLE ##tmpCouponsSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryCoupons>
	</cffunction>

	<cffunction name="getCouponDetailByCouponID" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		
		<cfset var qryCouponDetail = "">

		<cfquery name="qryCouponDetail" datasource="#application.dsn.memberCentral.dsn#">
			SELECT couponID, [status], couponCode, redeemDetail, invoiceDetail, unavailableDetail, invalidDetail, nonqualifyDetail, 
				startDate, endDate, maxOverallUsageCount, maxMemberUsageCount, pctOff, pctOffMaxOff, amtOff, dateAdded, 
				subscriptionsXML, eventsXML, storeXML, seminarwebXML
			FROM dbo.tr_coupons
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#"> 
			AND couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
		</cfquery>

		<cfreturn qryCouponDetail>
	</cffunction>

	<cffunction name="generateCouponCode" access="public" output="false" returntype="string">
		<cfset var couponCode = "">

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="getUniqueCode">
			<cfprocparam type="Out" cfsqltype="CF_SQL_CHAR" variable="couponCode">
		</cfstoredproc>

		<cfreturn couponCode>
	</cffunction>

	<cffunction name="doesCouponCodeExist" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">

		<cfset var local = structNew()>
	
		<cfquery name="local.qryCoupon" datasource="#application.dsn.memberCentral.dsn#">
			SELECT couponID
			FROM dbo.tr_coupons
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#"> 
			AND couponCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.couponCode#">
			<cfif arguments.couponID gt 0>
				AND couponID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
			</cfif>
		</cfquery>

		<cfset local.data.duplicate = local.qryCoupon.recordCount gt 0>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertCoupon" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="discountType" type="string" required="true">
		<cfargument name="pctOff" type="string" required="true">
		<cfargument name="pctOffMaxOff" type="numeric" required="true">
		<cfargument name="amtOff" type="numeric" required="true">
		<cfargument name="redeemDetail" type="string" required="true">
		<cfargument name="invoiceDetail" type="string" required="true">
		<cfargument name="nonqualifyDetail" type="string" required="true">
		<cfargument name="startDate" type="string" required="true">
		<cfargument name="endDate" type="string" required="true">
		<cfargument name="unavailableDetail" type="string" required="true">
		<cfargument name="maxOverallUsageCount" type="numeric" required="true">
		<cfargument name="maxMemberUsageCount" type="numeric" required="true">
		<cfargument name="invalidDetail" type="string" required="true">

		<cfset var couponID = 0>

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_createCoupon">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.couponCode)#">
			<cfif arguments.discountType EQ "P">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.pctOff#">
				<cfif len(arguments.pctOffMaxOff) gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(arguments.pctOffMaxOff,"0.00")#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				</cfif>				
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(arguments.amtOff,"0.00")#">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.redeemDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invoiceDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.nonqualifyDetail#">
			<cfif len(arguments.startDate) gt 0 and len(arguments.endDate) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.startDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.endDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.unavailableDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxOverallUsageCount#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxMemberUsageCount#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invalidDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="couponID">
		</cfstoredproc>

		<cfreturn couponID>
	</cffunction>
	
	<cffunction name="updateCoupon" access="public" output="false" returntype="void">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="discountType" type="string" required="true">
		<cfargument name="pctOff" type="string" required="true">
		<cfargument name="pctOffMaxOff" type="numeric" required="true">
		<cfargument name="amtOff" type="numeric" required="true">
		<cfargument name="redeemDetail" type="string" required="true">
		<cfargument name="invoiceDetail" type="string" required="true">
		<cfargument name="nonqualifyDetail" type="string" required="true">
		<cfargument name="startDate" type="string" required="true">
		<cfargument name="endDate" type="string" required="true">
		<cfargument name="unavailableDetail" type="string" required="true">
		<cfargument name="maxOverallUsageCount" type="numeric" required="true">
		<cfargument name="maxMemberUsageCount" type="numeric" required="true">
		<cfargument name="invalidDetail" type="string" required="true">

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_updateCoupon">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.couponCode)#">
			<cfif arguments.discountType EQ "P">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.pctOff#">
				<cfif len(arguments.pctOffMaxOff) gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(arguments.pctOffMaxOff,"0.00")#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				</cfif>				
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(arguments.amtOff,"0.00")#">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.redeemDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invoiceDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.nonqualifyDetail#">
			<cfif len(arguments.startDate) gt 0 and len(arguments.endDate) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.startDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.endDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.unavailableDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxOverallUsageCount#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxMemberUsageCount#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invalidDetail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="deleteCoupon" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="couponID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cftry>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='CouponAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

			<cfif not local.tmpRights.manageCoupons>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="tr_deleteCoupon" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="reactivateCoupon" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="couponID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cftry>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='CouponAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

			<cfif not local.tmpRights.manageCoupons>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="tr_reactivateCoupon" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCouponUsageDetails" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="usage" type="string" required="true" hint="all|subs|events|store|semweb">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryCoupon" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;

				SELECT sr.siteID, c.eventsXML, c.storeXML, c.seminarwebXML, CASE WHEN c.subscriptionsXML IS NOT NULL THEN 1 ELSE 0 END AS hasSubs
				FROM dbo.tr_coupons as c
				INNER JOIN dbo.cms_siteResources as sr on sr.siteID = @siteID
					and sr.siteResourceID = c.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				WHERE c.couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
				AND srs.siteResourceStatusDesc <> 'Deleted';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif NOT local.qryCoupon.recordCount>
				<cfreturn { "success":false }>
			</cfif>

			<cfif listFindNoCase("all,subs", arguments.usage)>
				<cfif local.qryCoupon.hasSubs>
					<cfset local.data["associatedsubs"] = getAssociatedCouponSubs(siteID=arguments.mcproxy_siteID, orgID=arguments.mcproxy_orgID, couponID=arguments.couponID)>
				<cfelse>
					<cfset local.data["associatedsubs"] = "">
				</cfif>
			</cfif>
			<cfif listFindNoCase("all,events", arguments.usage)>
				<cfif len(local.qryCoupon.eventsXML)>
					<cfset local.data["associatedevents"] = getAssociatedCouponEvents(siteID=arguments.mcproxy_siteID, eventsXML=local.qryCoupon.eventsXML)>
				<cfelse>
					<cfset local.data["associatedevents"] = "">
				</cfif>
			</cfif>
			<cfif listFindNoCase("all,store", arguments.usage)>
				<cfif len(local.qryCoupon.storeXML)>
					<cfset local.data["associatedstore"] = getAssociatedCouponStore(siteID=arguments.mcproxy_siteID, storeXML=local.qryCoupon.storeXML)>
				<cfelse>
					<cfset local.data["associatedstore"] = "">
				</cfif>
			</cfif>
			<cfif listFindNoCase("all,semweb", arguments.usage)>
				<cfif len(local.qryCoupon.seminarwebXML)>
					<cfset local.data["associatedsemweb"] = getAssociatedCouponSemWeb(siteID=arguments.mcproxy_siteID, SWXML=local.qryCoupon.seminarwebXML)>
				<cfelse>
					<cfset local.data["associatedsemweb"] = "">
				</cfif>
			</cfif>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset local.data["success"] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAssociatedCouponSubs" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.arrSubs = getCouponSubscriptions(siteID=arguments.siteID, orgID=arguments.orgID, couponID=arguments.couponID, mode='summary')>

		<cfsavecontent variable="local.subDefinition">
			<cfif arrayLen(local.arrSubs)>
				<cfoutput><h6 class="mb-1"><cfif local.arrSubs[1].applyTo EQ 'sub'>Specific Subscription(s)<cfelse>Entire Subscription Tree (Includes Parent and Add-ons)</cfif></h6></cfoutput>
				<cfoutput><div class="text-dim small mb-3"><cfif local.arrSubs[1].applyTo EQ 'sub'>The coupon will only apply to sales tied to the filtered subscription(s).<cfelse>Discounts will be applied to the selected subscription(s) and add-ons tied to the subscription(s).</cfif></div></cfoutput>
				<cfoutput><table class="table table-sm table-bordered table-striped" style="font-size:13px;"></cfoutput>
				<cfloop array="#local.arrSubs#" index="local.thisSub">
					<cfoutput><tr><td>#local.thisSub.subDef#</td></tr></cfoutput>
				</cfloop>
				<cfoutput></table></cfoutput>
			<cfelse>
				<cfoutput><div>No Subscriptions selected.</div></cfoutput>
			</cfif>
		</cfsavecontent>
		<cfset local.subDefinition = REReplace(local.subDefinition, "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

		<cfreturn local.subDefinition>
	</cffunction>

	<cffunction name="getAssociatedCouponEvents" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventsXML" type="xml" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryEventsXML" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				DECLARE @eventsXML XML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.eventsXML#">;

				select isnull(
					(select 
						(
						select distinct [cal].calendarID as id,
							ai.applicationInstanceName + 
							CASE 
								WHEN communityInstances.applicationInstanceName is not null 
									THEN ' (' + communityInstances.applicationInstanceName + ')'
								ELSE ''
							END as label
						from @eventsXML.nodes('/event/cl') as E(cl)
						cross apply dbo.fn_varCharListToTable(E.cl.value('.','varchar(max)'),',') as dg
						inner join dbo.ev_calendars as [cal] on [cal].siteID = @siteID and [cal].calendarID = dg.listitem
						inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and [cal].applicationInstanceID = ai.applicationInstanceID
						inner join dbo.cms_siteResources as sr on sr.siteID = @siteID 
							and ai.siteResourceID = sr.siteResourceID
							and sr.siteResourceStatusID = 1
						inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID 
							and parentResource.siteResourceID = sr.parentSiteResourceID
							and parentResource.siteResourceStatusID = 1
						left outer join dbo.cms_siteResources as grandparentResource
							inner join dbo.cms_applicationInstances as CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
							inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = grandparentResource.resourceTypeID and srt.resourceType = 'Community'
							on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
						FOR XML AUTO, ROOT('calendars'), TYPE
						),
						(
						select distinct [cat].categoryID as id, [cat].category as label
						from @eventsXML.nodes('/event/ct') as E(ct)
						cross apply dbo.fn_varCharListToTable(E.ct.value('.','varchar(max)'),',') as dg
						inner join dbo.ev_categories as [cat] on [cat].categoryID = dg.listitem
						FOR XML AUTO, ROOT('categories'), TYPE
						),
						(
						select distinct [event].eventID as id, eventContent.contentTitle as label
						from @eventsXML.nodes('/event/ev/e') as E(ev)
						cross apply dbo.fn_varCharListToTable(E.ev.value('.','varchar(max)'),',') as dg
						inner join dbo.ev_events as [event] on [event].eventID = dg.listitem
						inner join dbo.cms_contentLanguages as eventContent on eventContent.contentID = [event].eventContentID and eventContent.languageID = 1
						FOR XML AUTO, ROOT('events'), TYPE
						),
						(
						select distinct [rate].rateID as id, [rate].rateName as label
						from @eventsXML.nodes('/event/r') as E(r)
						cross apply dbo.fn_varCharListToTable(E.r.value('.','varchar(max)'),',') as dg
						inner join dbo.ev_rates as [rate] on [rate].rateID = dg.listitem
						inner join dbo.cms_siteResources as sr on sr.siteResourceID = [rate].siteResourceID
						inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
						FOR XML AUTO, ROOT('rates'), TYPE
						),
						(
						select distinct E.dts.value('.','varchar(max)') as label
						from @eventsXML.nodes('/event/dts') as E(dts)
						),
						(
						select distinct E.dte.value('.','varchar(max)') as label
						from @eventsXML.nodes('/event/dte') as E(dte)
						)
					for XML PATH(''), root('eventdefs'), TYPE)
				,'<eventdefs/>') as condXML;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfset local.eventsDefXML = XMLParse(local.qryEventsXML.condXML)>

		<cfsavecontent variable="local.eventsDefinition">
			<cfset local.cl_list = XMLSearch(arguments.eventsXML,'string(/event/cl)')>
			<cfset local.ct_list = XMLSearch(arguments.eventsXML,'string(/event/ct)')>
			<cfset local.dateStart = XMLSearch(arguments.eventsXML,'string(/event/dts)')>
			<cfset local.dateEnd = XMLSearch(arguments.eventsXML,'string(/event/dte)')>
			<cfset local.arrEvents = XMLSearch(arguments.eventsXML,'/event/ev/e')>
			<cfset local.r_list = XMLSearch(arguments.eventsXML,'string(/event/r)')>
			<cfif NOT arrayLen(local.arrEvents)>
				<cfif listLen(local.cl_list)>
					<cfset local.cl_listDesc = ''>
					<cfloop list="#local.cl_list#" index="local.thisVal">
						<cfset local.cl_listDesc = listAppend(local.cl_listDesc,XMLSearch(local.eventsDefXML,"string(//calendars/cal[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>Calendars: #ListChangeDelims(local.cl_listDesc,", ",chr(7))#</cfoutput>
				</cfif>			
				<cfif listLen(local.ct_list)>
					<cfset local.ct_listDesc = ''>
					<cfloop list="#local.ct_list#" index="local.thisVal">
						<cfset local.ct_listDesc = listAppend(local.ct_listDesc,XMLSearch(local.eventsDefXML,"string(//categories/cat[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>; Calendar Types: #ListChangeDelims(local.ct_listDesc,", ",chr(7))#</cfoutput>
				</cfif>			
				<cfif listLen(local.dateStart)>
					<cfoutput>; Event Start Date: #local.dateStart#</cfoutput>
				</cfif>			
				<cfif listLen(local.dateEnd)>
					<cfoutput>; Event End Date: #local.dateEnd#</cfoutput>
				</cfif>
			</cfif>
			<cfif arrayLen(local.arrEvents)>
				<cfset local.ev_listDesc = ''>
				<cfloop array="#local.arrEvents#" index="local.thisXMLElem">
					<cfset local.ev_listDesc = listAppend(local.ev_listDesc,XMLSearch(local.eventsDefXML,"string(//events/event[@id='#local.thisXMLElem.XmlText#']/eventContent/@label)"),chr(7))>
				</cfloop>
				<cfoutput>Events: #ListChangeDelims(local.ev_listDesc,", ",chr(7))#</cfoutput>
				<cfif listLen(local.r_list)>
					<cfset local.r_listDesc = ''>
					<cfloop list="#local.r_list#" index="local.thisVal">
						<cfset local.r_listDesc = listAppend(local.r_listDesc,XMLSearch(local.eventsDefXML,"string(//rates/rate[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>; Rates: #ListChangeDelims(local.r_listDesc,", ",chr(7))#</cfoutput>
				</cfif>
			</cfif>		
		</cfsavecontent>

		<cfreturn local.eventsDefinition>
	</cffunction>

	<cffunction name="getAssociatedCouponStore" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="storeXML" type="xml" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryStoreXML" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID INT, @storeXML XML, @storeID INT;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @storeXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.storeXML#">;

				select @storeID = storeID
				from dbo.store
				where siteID = @siteID;

				select isnull(
					(select 
						(
						select distinct [cat].categoryID as id, [cat].thePathExpanded as label
						from @storeXML.nodes('/store/ct') as S(ct)
						cross apply dbo.fn_varCharListToTable(S.ct.value('.','varchar(max)'),',') as dg
						cross apply dbo.fn_getRecursiveStoreCategories(@storeID, NULL, NULL) as [cat] 
						where [cat].categoryID = dg.listitem
						FOR XML AUTO, ROOT('categories'), TYPE
						),
						(
						select distinct p.itemID as id, pc.contentTitle as label
						from @storeXML.nodes('/store/p/i') as S(p)
						cross apply dbo.fn_varCharListToTable(S.p.value('.','varchar(max)'),',') as dg
						inner join dbo.store_products as p on p.storeID = @storeID and p.itemID = dg.listitem
						inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
						FOR XML AUTO, ROOT('products'), TYPE
						),
						(
						select distinct pf.Formatid as id, pf.Name as label
						from @storeXML.nodes('/store/pf') as S(pf)
						cross apply dbo.fn_varCharListToTable(S.pf.value('.','varchar(max)'),',') as dg
						inner join dbo.store_productformats as pf on pf.Formatid = dg.listitem and pf.status <> 'D'
						FOR XML AUTO, ROOT('productformat'), TYPE
						)
					for XML PATH(''), root('storedefs'), TYPE)
				,'<storedefs />') as condXML;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfset local.storeDefXML = XMLParse(local.qryStoreXML.condXML)>

		<cfsavecontent variable="local.storeDefinition">
			<cfset local.ct_list = XMLSearch(arguments.storeXML,'string(/store/ct)')>
			<cfset local.arrProducts = XMLSearch(arguments.storeXML,'/store/p/i')>
			<cfset local.format_list = XMLSearch(arguments.storeXML,'string(/store/pf)')>
			<cfif NOT arrayLen(local.arrProducts)>
				<cfif listLen(local.ct_list)>
					<cfset local.ct_listDesc = ''>
					<cfloop list="#local.ct_list#" index="local.thisVal">
						<cfset local.ct_listDesc = listAppend(local.ct_listDesc,XMLSearch(local.storeDefXML,"string(//categories/cat[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>Categories: #ListChangeDelims(local.ct_listDesc,", ",chr(7))#</cfoutput>
				</cfif>
			</cfif>						
			<cfif arrayLen(local.arrProducts)>
				<cfset local.product_listDesc = ''>
				<cfloop array="#local.arrProducts#" index="local.thisXMLElem">
					<cfset local.product_listDesc = listAppend(local.product_listDesc,XMLSearch(local.storeDefXML,"string(//products/p[@id='#local.thisXMLElem.XmlText#']/pc/@label)"),chr(7))>
				</cfloop>
				<cfoutput>Products: #ListChangeDelims(local.product_listDesc,", ",chr(7))#</cfoutput>
				<cfif listLen(local.format_list)>
					<cfset local.format_listDesc = ''>
					<cfloop list="#local.format_list#" index="local.thisVal">
						<cfset local.format_listDesc = listAppend(local.format_listDesc,XMLSearch(local.storeDefXML,"string(//productformat/pf[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>; Formats: #ListChangeDelims(local.format_listDesc,", ",chr(7))#
					</cfoutput>
				</cfif>
			</cfif>		
		</cfsavecontent>

		<cfreturn local.storeDefinition>
	</cffunction>

	<cffunction name="getAssociatedCouponSemWeb" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="SWXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.swType = XMLSearch(arguments.SWXML, "string(/sw/t)")>

		<cfquery name="local.qrySWXML" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@SWXML XML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.SWXML#">,
					@participantID int;

				SELECT @participantID = p.participantID
				FROM dbo.tblParticipants AS p
				INNER JOIN membercentral.dbo.sites AS s ON s.siteCode = p.orgCode
				WHERE s.siteID = @siteID
				AND p.isActive = 1;

				select isnull(
					(select 
						(
						select SW.t.value('.','varchar(4)') as id,
							case SW.t.value('.','varchar(4)') 
								when 'SWL' then 'SeminarWeb Live'
								when 'SWOD' then 'SeminarWeb OnDemand'
								when 'SWB' then 'SeminarWeb Bundles'
								else '' end as label
						from @SWXML.nodes('/sw/t') as SW(t)
						FOR XML AUTO, ROOT('format'), TYPE
						),
						(
						select distinct 
							<cfif local.swType eq 'SWB'>
								p.bundleID as id, p.bundleName as label
							<cfelse>
								p.seminarID as id, p.seminarName as label
							</cfif>
						from @SWXML.nodes('/sw/pid/p') as SW(pid)
						cross apply membercentral.dbo.fn_varCharListToTable(SW.pid.value('.','varchar(max)'),',') as dg
						<cfif local.swType eq 'SWB'>
							inner join dbo.tblBundles as p on p.bundleID = dg.listitem
						<cfelse>
							inner join dbo.tblSeminars as p on p.seminarID = dg.listitem
						</cfif>
						FOR XML AUTO, ROOT('program'), TYPE
						),
						(
						select distinct [rate].rateID as id, [rate].rateName as label
						from @SWXML.nodes('/sw/r') as SW(r)
						cross apply membercentral.dbo.fn_varCharListToTable(SW.r.value('.','varchar(max)'),',') as dg
						<cfif local.swType eq 'SWB'>
							inner join dbo.tblBundlesAndRates as [rate] on [rate].rateID = dg.listitem
						<cfelse>
							inner join dbo.tblSeminarsAndRates as [rate] on [rate].rateID = dg.listitem
						</cfif>
							and [rate].participantID = @participantID
						inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = [rate].siteResourceID
						inner join membercentral.dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
						FOR XML AUTO, ROOT('rates'), TYPE
						),
						(
						select distinct E.dts.value('.','varchar(max)') as label
						from @SWXML.nodes('/sw/dts') as E(dts)
						),
						(
						select distinct E.dte.value('.','varchar(max)') as label
						from @SWXML.nodes('/sw/dte') as E(dte)
						)
					for XML PATH(''), root('swdefs'), TYPE)
				,'<swdefs />') as condXML;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfset local.SWDefXML = XMLParse(local.qrySWXML.condXML)>

		<cfsavecontent variable="local.SWDefinition">
			<cfset local.format = XMLSearch(arguments.SWXML,'string(/sw/t)')>
			<cfset local.participantsList = XMLSearch(arguments.SWXML,'string(/sw/p)')>
			<cfset local.dateStart = XMLSearch(arguments.SWXML,'string(/sw/dts)')>
			<cfset local.dateEnd = XMLSearch(arguments.SWXML,'string(/sw/dte)')>
			<cfset local.arrPrograms = XMLSearch(arguments.SWXML,'/sw/pid/p')>
			<cfset local.ratesList = XMLSearch(arguments.SWXML,'string(/sw/r)')>
			<cfoutput>#XMLSearch(local.SWDefXML,"string(//format/SW[@id='#local.format#']/@label)")#: <br/></cfoutput>
			<cfif NOT arrayLen(local.arrPrograms)>
				<cfif listLen(local.participantsList)>
					<cfset local.participantsDesc = ''>
					<cfloop list="#local.participantsList#" index="local.thisVal">
						<cfset local.participantsDesc = listAppend(local.participantsDesc,XMLSearch(local.SWDefXML,"string(//participants/SW[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>Publishers: #ListChangeDelims(local.participantsDesc,", ",chr(7))#</cfoutput>
				</cfif>
				<cfif len(local.dateStart)>
					<cfoutput><cfif listLen(local.participantsList)>; </cfif>Program Start Date: #local.dateStart#</cfoutput>
				</cfif>
				<cfif len(local.dateEnd)>
					<cfoutput><cfif listLen(local.participantsList) or len(local.dateStart)>; </cfif>Program End Date: #local.dateEnd#</cfoutput>
				</cfif>
			</cfif>
			<cfif arrayLen(local.arrPrograms)>
				<cfset local.programListDesc = ''>
				<cfloop array="#local.arrPrograms#" index="local.thisXMLElem">
					<cfset local.programListDesc = listAppend(local.programListDesc,XMLSearch(local.SWDefXML,"string(//program/p[@id='#local.thisXMLElem.XmlText#']/@label)"),chr(7))>
				</cfloop>
				<cfoutput>Programs: #ListChangeDelims(local.programListDesc,", ",chr(7))#</cfoutput>
				<cfif listLen(local.ratesList)>
					<cfset local.ratesListDesc = ''>
					<cfloop list="#local.ratesList#" index="local.thisVal">
						<cfset local.ratesListDesc = listAppend(local.ratesListDesc,XMLSearch(local.SWDefXML,"string(//rates/rate[@id='#local.thisVal#']/@label)"),chr(7))>
					</cfloop>
					<cfoutput>; Rates: #ListChangeDelims(local.ratesListDesc,", ",chr(7))#</cfoutput>
				</cfif>
			</cfif>
		</cfsavecontent>

		<cfreturn local.SWDefinition>
	</cffunction>

	<cffunction name="getCouponUsageXML" access="public" output="false" returntype="string">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="usage" type="string" required="true">
		
		<cfset var qryCoupon = "">

		<cfquery name="qryCoupon" datasource="#application.dsn.memberCentral.dsn#">
			SELECT subscriptionsXML, eventsXML, storeXML, seminarwebXML
			FROM dbo.tr_coupons
			WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
		</cfquery>

		<cfswitch expression="#arguments.usage#">
			<cfcase value="subs">
				<cfreturn qryCoupon.subscriptionsXML>
			</cfcase>
			<cfcase value="events">
				<cfreturn qryCoupon.eventsXML>
			</cfcase>
			<cfcase value="store">
				<cfreturn qryCoupon.storeXML>
			</cfcase>
			<cfcase value="semweb">
				<cfreturn qryCoupon.seminarwebXML>
			</cfcase>
		</cfswitch>
	</cffunction>

	<cffunction name="clearCouponUsage" access="public" output="false" returntype="struct">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="usage" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfswitch expression="#arguments.usage#">
				<cfcase value="subs">
					<cfquery name="local.qryUpdateCoupon" datasource="#application.dsn.memberCentral.dsn#">
						UPDATE dbo.tr_coupons
						SET subscriptionsXML = NULL
						WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
					</cfquery>
				</cfcase>
				<cfcase value="events">
					<cfquery name="local.qryUpdateCoupon" datasource="#application.dsn.memberCentral.dsn#">
						UPDATE dbo.tr_coupons
						SET eventsXML = NULL
						WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
					</cfquery>
				</cfcase>
				<cfcase value="store">
					<cfquery name="local.qryUpdateCoupon" datasource="#application.dsn.memberCentral.dsn#">
						UPDATE dbo.tr_coupons
						SET storeXML = NULL
						WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
					</cfquery>
				</cfcase>
				<cfcase value="semweb">
					<cfquery name="local.qryUpdateCoupon" datasource="#application.dsn.memberCentral.dsn#">
						UPDATE dbo.tr_coupons
						SET seminarwebXML = NULL
						WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">
					</cfquery>
				</cfcase>
			</cfswitch>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCouponSubscriptions" access="public" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.arrSubs = []>
		<cfset local.subXML = getCouponUsageXML(couponID=arguments.couponID, usage='subs')>
		<cfset local.applyTo = len(local.subXML) ? XMLSearch(local.subXML,"string(/cs/applyto)") : ''>

		<cfif len(local.subXML)>
			<cfquery name="local.qrySubs" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @defID int, @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				DECLARE @subDefs TABLE (defID int IDENTITY(1,1), defUID varchar(36));
				DECLARE @subTypes TABLE (defID int, typeID int);
				DECLARE @subs TABLE (defID int, subscriptionID int);
				DECLARE @subRates TABLE (defID int, rateID int);

				<cfloop array="#xmlSearch(local.subXML,"/cs/subdefs/subs")#" index="local.thisSubDef">
					INSERT INTO @subDefs (defUID)
					VALUES ('#local.thisSubDef.XmlAttributes.id#');

					SET @defID = SCOPE_IDENTITY();

					<cfloop array="#local.thisSubDef.xmlChildren#" index="local.thisSub">
						<cfif local.thisSub.xmlName EQ 't'>
							INSERT INTO @subTypes (defID, typeID)
							SELECT DISTINCT @defID, listitem
							FROM dbo.fn_intListToTable('#local.thisSub.XmlText#',',');
						<cfelseif local.thisSub.xmlName EQ 's'>
							INSERT INTO @subs (defID, subscriptionID)
							SELECT DISTINCT @defID, listitem
							FROM dbo.fn_intListToTable('#local.thisSub.XmlText#',',');
						<cfelseif local.applyTo EQ 'sub' AND local.thisSub.xmlName EQ 'r'>
							INSERT INTO @subRates (defID, rateID)
							SELECT DISTINCT @defID, listitem
							FROM dbo.fn_intListToTable('#local.thisSub.XmlText#',',');
						</cfif>
					</cfloop>
				</cfloop>

				SELECT DISTINCT d.defUID, d.defID, 0 AS typeID, 'All Subscription Types' AS typeName, 
					0 AS subscriptionID, '' AS subscriptionName, 0 AS rateID, '' AS rateName, 0 AS hasTypeSelection
				FROM @subDefs AS d
				LEFT OUTER JOIN @subTypes AS t ON t.defID = d.defID
				WHERE ISNULL(t.typeID,0) = 0
					UNION
				SELECT DISTINCT d.defUID, d.defID, t.typeID, st.typeName, s.subscriptionID,
					ss.subscriptionName, r.rateID, r.rateName, 1 as hasTypeSelection
				FROM @subDefs AS d
				INNER JOIN @subTypes AS t ON t.defID = d.defID
				INNER JOIN dbo.sub_types AS st ON st.siteID = @siteID
					AND st.typeID = t.typeID
				LEFT OUTER JOIN @subs AS s
					INNER JOIN dbo.sub_subscriptions AS ss ON ss.orgID = @orgID
						AND ss.subscriptionID = s.subscriptionID
					ON s.defID = d.defID
				LEFT OUTER JOIN @subRates AS sr
					LEFT OUTER JOIN dbo.sub_rates AS r ON r.rateID = sr.rateID
					ON sr.defID = d.defID
				ORDER BY hasTypeSelection, defID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfoutput query="local.qrySubs" group="defID">
				<cfset local.defID = local.qrySubs.defID>
				
				<cfquery name="local.qryThisTypes" dbtype="query">
					SELECT DISTINCT typeID, typeName
					FROM [local].qrySubs
					WHERE defID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.defID#">
					ORDER BY typeName
				</cfquery>
				<cfquery name="local.qryThisSubs" dbtype="query">
					SELECT DISTINCT subscriptionID, subscriptionName
					FROM [local].qrySubs
					WHERE defID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.defID#">
					AND subscriptionName <> ''
					ORDER BY subscriptionName
				</cfquery>
				<cfset local.defStr = { "typeIDList":valueList(local.qryThisTypes.typeID), "subIDList": local.qryThisSubs.recordCount ? valueList(local.qryThisSubs.subscriptionID) : 0 }>
				<cfif local.applyTo EQ 'sub'>
					<cfquery name="local.qryThisSubRates" dbtype="query">
						SELECT DISTINCT rateID, rateName
						FROM [local].qrySubs
						WHERE defID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.defID#">
						AND rateName <> ''
						ORDER BY rateName
					</cfquery>
					<cfset local.defStr['rateIDList'] = local.qryThisSubRates.recordCount ? valueList(local.qryThisSubRates.rateID) : 0>
				</cfif>

				<cfset local.subDef = "Type: #replace(valueList(local.qryThisTypes.typeName),',',', ','all')#;">
				<cfif local.qryThisSubs.recordCount>
					<cfset local.subDef = "#local.subDef# Subscription: #replace(valueList(local.qryThisSubs.subscriptionName),',',', ','all')#;">
				<cfelseif local.qryThisTypes.recordCount EQ 1 AND val(local.qrySubs.typeID) GT 0>
					<cfset local.subDef = "#local.subDef# Subscription: All Subscriptions;">
				</cfif>
				<cfif local.applyTo EQ 'sub'>
					<cfif local.qryThisSubRates.recordCount>
						<cfset local.subDef = "#local.subDef# Rate: #replace(valueList(local.qryThisSubRates.rateName),',',', ','all')#;">
					<cfelseif local.qryThisSubs.recordCount>
						<cfset local.subDef = "#local.subDef# Rate: All Rates;">
					</cfif>
				</cfif>
				
				<cfif arguments.mode EQ 'datatable'>
					<cfset local.arrSubs.append({ 
						"defuid": local.qrySubs.defUID, 
						"subDef": local.subDef, 
						"DT_RowId": "cpnsubrow#local.qrySubs.defUID#", 
						"DT_RowData":{ "defJSON":serializeJSON(local.defStr) }  
					})>
				<cfelse>
					<cfset local.arrSubs.append({ "defuid": local.qrySubs.defUID, "subDef": local.subDef, "applyto":local.applyTo })>
				</cfif>
			</cfoutput>
		</cfif>

		<cfreturn local.arrSubs>
	</cffunction>

	<cffunction name="associateSubsToCoupons" access="public" output="true" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="applyto" type="string" required="true" hint="sub or subtree">
		<cfargument name="defUID" type="string" required="true" hint="sub definition uid">
		<cfargument name="fSubTypes" type="string" required="true">
		<cfargument name="fSubs" type="string" required="true">
		<cfargument name="fSubRates" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.subXML = getCouponUsageXML(couponID=arguments.couponID, usage='subs')>
		
		<cftry>
			<cfscript>
				local.origApplyTo = len(local.subXML) ? XMLSearch(local.subXML,"string(/cs/applyto)") : '';

				// initial add or switched choice (specific sub / sub tree)
				if (NOT len(local.subXML) OR local.origApplyTo NEQ arguments.applyto) {
					cfxml(variable="local.subXML") {
						writeOutput('<cs><applyto>#arguments.applyto#</applyto><subdefs></subdefs></cs>');
					}
					arguments.defUID = '';
				} else {
					local.subXML = XMLParse(local.subXML);
				}

				if (listLen(arguments.fSubTypes) GT 1 AND listFind(arguments.fSubTypes,0)) {
					arguments.fSubTypes = 0;
					arguments.fSubs = 0;
					arguments.fSubRates = 0;
				}
				if (listLen(arguments.fSubs) GT 1 AND listFind(arguments.fSubs,0)) {
					arguments.fSubs = 0;
					arguments.fSubRates = 0;
				}
				if (listLen(arguments.fSubRates) GT 1 AND listFind(arguments.fSubRates,0)) {
					arguments.fSubRates = 0;
				}
				
				if (NOT len(arguments.defUID)) {
					local.subDefNode = XMLSearch(local.subXML,"/cs/subdefs");
					local.newNode = XMLElemNew(local.subXML,"subs");
					local.newNode.xmlAttributes['id'] = createUUID();
					arrayAppend(local.subDefNode[1].xmlChildren,local.newNode);
					local.subNode = XMLSearch(local.subDefNode[1],"subs[@id='#local.newNode.xmlAttributes['id']#']");
		
					local.newNode = XMLElemNew(local.subXML,"t");
					local.newNode.xmlText = arguments.fSubTypes;
					arrayAppend(local.subNode[1].xmlChildren,local.newNode);
		
					local.newNode = XMLElemNew(local.subXML,"s");
					local.newNode.xmlText = arguments.fSubs;
					arrayAppend(local.subNode[1].xmlChildren,local.newNode);
		
					if (arguments.applyto EQ 'sub') {
						local.newNode = XMLElemNew(local.subXML,"r");
						local.newNode.xmlText = arguments.fSubRates;
						arrayAppend(local.subNode[1].xmlChildren,local.newNode);
					}
		
					local.subDefXML = getCouponSubDefinitionXML(subXML=local.subXML);
					local.subDisplayLabel = getCouponSubNodeDisplayValue(subNode=local.subNode[1], subDefXML=local.subDefXML);
					local.changeLogMessage = "Added new Subscription definition: [#trim(local.subDisplayLabel)#].";
				} else {
					local.subNode = XMLSearch(local.subXML,"/cs/subdefs/subs[@id='#arguments.defUID#']");
		
					local.subDefXML = getCouponSubDefinitionXML(subXML=local.subXML);
					local.subDisplayLabelOld = getCouponSubNodeDisplayValue(subNode=local.subNode[1], subDefXML=local.subDefXML);
		
					local.subNode[1].t.xmlText = arguments.fSubTypes;
					local.subNode[1].s.xmlText = arguments.fSubs;
					if (arguments.applyto EQ 'sub') { 
						local.subNode[1].r.xmlText = arguments.fSubRates;
					}
					
					local.subDefXML = getCouponSubDefinitionXML(subXML=local.subXML);
					local.subDisplayLabelNew = getCouponSubNodeDisplayValue(subNode=local.subNode[1], subDefXML=local.subDefXML);
				
					local.changeLogMessage = '';
					if(local.subDisplayLabelOld NEQ local.subDisplayLabelNew) {
						local.changeLogMessage = "Subscription definition changed from [#trim(local.subDisplayLabelOld)#] to [#trim(local.subDisplayLabelNew)#].";
					}
				}

				updateCouponSubXML(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, couponID=arguments.couponID, subXML=local.subXML, auditMsg=local.changeLogMessage);
				
				local.data.success = true;
				</cfscript>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateCouponSubXML" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="auditMsg" type="string" required="true">

		<cfset var local = structNew()>
		<!--- remove the <xml> tag, specifically the encoding. --->
		<cfset local.subXML = replaceNoCase(toString(arguments.subXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfquery name="local.qryUpdateSubXML" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int, @couponID int, @crlf varchar(10), @msgjson varchar(max),
					@recordedByMemberID int;
				
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.couponID#">;
				SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				SET @crlf = char(13) + char(10);

				SELECT @msgjson = 'Coupon [' + couponCode + '] has been updated.'
				FROM dbo.tr_coupons
				WHERE couponID = @couponID;

				SELECT @msgjson = @msgjson + @crlf + 'The following changes have been made:' + @crlf + 
								dbo.fn_cleanInvalidXMLChars(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.auditMsg#">)
				
				BEGIN TRAN;
					UPDATE dbo.tr_coupons
					SET subscriptionsXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subXML#">
					WHERE couponID = @couponID;

					<cfif len(arguments.auditMsg)>
						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						VALUES ('{ "c":"auditLog", "d": {
							"AUDITCODE":"COUPON",
							"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
							"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
							"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
							"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
					</cfif>
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getCouponSubDefinitionXML" access="public" output="false" returntype="xml">
		<cfargument name="subXML" type="xml" required="true">

		<cfset var qryCondsXML = "">

		<cfquery name="local.qrySubXML" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @subXML xml = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replaceNoCase(toString(arguments.subXML),'<?xml version="1.0" encoding="UTF-8"?>','')#">;

			SELECT isnull(
				(select 
					(
					select distinct typeid as id, typeName as label
					from @subXML.nodes('/cs/subdefs/subs/t') as C(cond)
					cross apply dbo.fn_varCharListToTable(C.cond.value('.','varchar(max)'),',') as dg
					inner join dbo.sub_types as [type] on [type].typeid = dg.listitem
					FOR XML AUTO, ROOT('types'), TYPE
					),
					(
					select distinct subscriptionid as id, subscriptionName as label
					from @subXML.nodes('/cs/subdefs/subs/s') as C(cond)
					cross apply dbo.fn_varCharListToTable(C.cond.value('.','varchar(max)'),',') as dg
					inner join dbo.sub_subscriptions as [sub] on [sub].subscriptionid = dg.listitem
					FOR XML AUTO, ROOT('subs'), TYPE
					),
					(
					select distinct rateid as id, rateName as label
					from @subXML.nodes('/cs/subdefs/subs/r') as C(cond)
					cross apply dbo.fn_varCharListToTable(C.cond.value('.','varchar(max)'),',') as dg
					inner join dbo.sub_rates as [rate] on [rate].rateID = dg.listitem
					FOR XML AUTO, ROOT('rates'), TYPE
					)
				for XML PATH(''), root('subdefs'), TYPE)
			,'<subdefs/>') as subXML;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn XMLParse(local.qrySubXML.subXML)>
	</cffunction>

	<cffunction name="getCouponSubNodeDisplayValue" access="public" output="false" returntype="string">
		<cfargument name="subNode" type="xml" required="true">
		<cfargument name="subDefXML" type="xml" required="true">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.condLabel">
			<cfset local.t_list = XMLSearch(arguments.subNode,'string(t)')>
			<cfif listLen(local.t_list)>
				<cfoutput>Types: </cfoutput>
				<cfset local.t_listDesc = ''>
				<cfloop list="#local.t_list#" index="local.thisVal">
					<cfset local.t_listDesc = listAppend(local.t_listDesc,XMLSearch(arguments.subDefXML,"string(//types/type[@id='#local.thisVal#']/@label)"),chr(7))>
				</cfloop>
				<cfoutput>#ListChangeDelims(local.t_listDesc,", ",chr(7))#</cfoutput>
			</cfif>
			<cfset local.s_list = XMLSearch(arguments.subNode,'string(s)')>
			<cfif listLen(local.s_list)>
				<cfoutput>; Subscriptions: </cfoutput>
				<cfset local.s_listDesc = ''>
				<cfloop list="#local.s_list#" index="local.thisVal">
					<cfset local.s_listDesc = listAppend(local.s_listDesc,XMLSearch(arguments.subDefXML,"string(//subs/sub[@id='#local.thisVal#']/@label)"),chr(7))>
				</cfloop>
				<cfoutput>#ListChangeDelims(local.s_listDesc,", ",chr(7))#</cfoutput>
			</cfif>
			<cfset local.r_list = XMLSearch(arguments.subNode,'string(r)')>
			<cfif listLen(local.r_list)>
				<cfoutput>; Rates: </cfoutput>
				<cfset local.r_listDesc = ''>
				<cfloop list="#local.r_list#" index="local.thisVal">
					<cfset local.r_listDesc = listAppend(local.r_listDesc,XMLSearch(arguments.subDefXML,"string(//rates/rate[@id='#local.thisVal#']/@label)"),chr(7))>
				</cfloop>
				<cfoutput>#ListChangeDelims(local.r_listDesc,", ",chr(7))#</cfoutput>
			</cfif>
		</cfsavecontent>
		<cfset local.condLabel = REReplace(local.condLabel, "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

		<cfreturn local.condLabel>
	</cffunction>

	<cffunction name="removeCouponSubs" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="defUID" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfset local.subXML = getCouponUsageXML(couponID=arguments.couponID, usage='subs')>
		<cfset local.subXML = XMLParse(local.subXML)>

		<cfset local.subConditionNode = XMLSearch(local.subXML,"/cs/subdefs/subs[@id='#ucase(arguments.defUID)#']")>
		<cfset local.condDefXML = getCouponSubDefinitionXML(subXML=local.subXML)>
		<cfset local.subDisplayLabel = getCouponSubNodeDisplayValue(subNode=local.subConditionNode[1], subDefXML=local.condDefXML)>
		<cfset application.objCommon.XmlDeleteNodes(local.subXML,local.subConditionNode)>
		
		<!--- remove the <xml> tag, specifically the encoding. It breaks under Railo. --->
 	 	<cfset local.subXML = replaceNoCase(toString(local.subXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfset updateCouponSubXML(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, couponID=arguments.couponID, subXML=local.subXML, 
				auditMsg="Subscription Definition [#trim(local.subDisplayLabel)#] has been removed.")>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="associateEventsToCoupons" access="public" output="true" returntype="struct">
		<cfargument name="fd" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.couponEvArgs = deserializeJSON(arguments.fd)>
		<cfset this.strUsages.showEvents = arrayLen(XMLSearch(application.adminNavigationXML,"/navitems/navitem[@navName='Events']")) gt 0>
		<cftry>
			<cfif this.strUsages.showEvents>
				<cfif len(trim(local.couponEvArgs.f_CL)) OR len(trim(local.couponEvArgs.f_CT)) OR len(trim(local.couponEvArgs.f_DTS)) OR len(trim(local.couponEvArgs.f_DTE)) OR len(trim(local.couponEvArgs.f_EV)) OR len(trim(local.couponEvArgs.f_R))>
					<cfxml variable="local.eventsXML">
						<cfoutput>
							<event>
								<cl>#local.couponEvArgs.f_CL#</cl>
								<ct>#local.couponEvArgs.f_CT#</ct>
								<dts>#local.couponEvArgs.f_DTS#</dts>
								<dte>#local.couponEvArgs.f_DTE#</dte>
								<ev>
									<cfif listLen(local.couponEvArgs.f_EV)>
										<cfloop list="#local.couponEvArgs.f_EV#" index="local.eventID">
											<e rc="#local.couponEvArgs.keyExists('redemptionCount_#local.eventID#') AND val(local.couponEvArgs['redemptionCount_#local.eventID#']) GT 0 ? int(val(local.couponEvArgs['redemptionCount_#local.eventID#'])) : 1#">#local.eventID#</e>
										</cfloop>
									</cfif>
								</ev>
								<r>#local.couponEvArgs.f_R#</r>
							</event>
						</cfoutput>
					</cfxml>

					<!--- remove the <xml> tag, specifically the encoding. --->
					<cfset local.eventsXML = replaceNoCase(toString(local.eventsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

					<cfquery name="local.qryUpdateEventsXML" datasource="#application.dsn.memberCentral.dsn#">
						UPDATE dbo.tr_coupons
						SET eventsXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.eventsXML#">
						WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.couponEvArgs.couponID#">
					</cfquery>
				<cfelse>
					<cfset clearCouponUsage(couponID=local.couponEvArgs.couponID, usage="events")>
				</cfif>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="associateStoreToCoupons" access="public" output="true" returntype="struct">
		<cfargument name="fd" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.couponStoreArgs = deserializeJSON(arguments.fd)>
		
		<cftry>
			<cfif len(trim(local.couponStoreArgs.f_CT)) OR len(trim(local.couponStoreArgs.f_P)) OR len(trim(local.couponStoreArgs.f_PF))>
				<cfxml variable="local.storeXML">
					<cfoutput>
						<store>
							<ct>#local.couponStoreArgs.f_CT#</ct>
							<p>
								<cfif listLen(local.couponStoreArgs.f_P)>
									<cfloop list="#local.couponStoreArgs.f_P#" index="local.itemID">
										<i rc="#local.couponStoreArgs.keyExists('redemptionCount_#local.itemID#') AND val(local.couponStoreArgs['redemptionCount_#local.itemID#']) GT 0 ? int(val(local.couponStoreArgs['redemptionCount_#local.itemID#'])) : 1#">#local.itemID#</i>
									</cfloop>
								</cfif>
							</p>
							<pf>#local.couponStoreArgs.f_PF#</pf>
						</store>
					</cfoutput>
				</cfxml>

				<!--- remove the <xml> tag, specifically the encoding. --->
				<cfset local.storeXML = replaceNoCase(toString(local.storeXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

				<cfquery name="local.qryUpdateStoreXML" datasource="#application.dsn.memberCentral.dsn#">
					UPDATE dbo.tr_coupons
					SET storeXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.storeXML#">
					WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.couponStoreArgs.couponID#">
				</cfquery>
			<cfelse>
				<cfset clearCouponUsage(couponID=local.couponStoreArgs.couponID, usage="store")>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="associateSemWebToCoupons" access="public" output="true" returntype="struct">
		<cfargument name="fd" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.couponSWArgs = deserializeJSON(arguments.fd)>
		
		<cftry>
			<cfif len(trim(local.couponSWArgs.f_T)) OR len(trim(local.couponSWArgs.f_DTS)) OR len(trim(local.couponSWArgs.f_DTE)) OR len(trim(local.couponSWArgs.f_PID)) OR len(trim(local.couponSWArgs.f_R))>
				<cfxml variable="local.SWXML">
					<cfoutput>
						<sw>
							<t>#local.couponSWArgs.f_T#</t>
							<dts>#local.couponSWArgs.f_DTS#</dts>
							<dte>#local.couponSWArgs.f_DTE#</dte>
							<pid>
								<cfif listLen(local.couponSWArgs.f_PID)>
									<cfloop list="#local.couponSWArgs.f_PID#" index="local.programID">
										<p rc="#local.couponSWArgs.keyExists('redemptionCount_#local.programID#') AND val(local.couponSWArgs['redemptionCount_#local.programID#']) GT 0 ? int(val(local.couponSWArgs['redemptionCount_#local.programID#'])) : 1#">#local.programID#</p>
									</cfloop>
								</cfif>
							</pid>
							<r>#local.couponSWArgs.f_R#</r>
						</sw>
					</cfoutput>
				</cfxml>

				<!--- remove the <xml> tag, specifically the encoding. --->
				<cfset local.SWXML = replaceNoCase(toString(local.SWXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

				<cfquery name="local.qryUpdateSWXML" datasource="#application.dsn.memberCentral.dsn#">
					UPDATE dbo.tr_coupons
					SET seminarwebXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.SWXML#">
					WHERE couponID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.couponSWArgs.couponID#">
				</cfquery>
			<cfelse>
				<cfset clearCouponUsage(couponID=local.couponSWArgs.couponID, usage="semweb")>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getStoreCategories" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryStoreCategories = "">

		<cfquery name="qryStoreCategories" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int, @storeID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select @storeID = s.storeID
			from dbo.store as s
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = s.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			where s.siteID = @siteID;

			select categoryID, categoryName, thePathExpanded
			from dbo.fn_getRecursiveStoreCategories(@storeID, NULL,	NULL)
			order by thepath;
		</cfquery>

		<cfreturn qryStoreCategories>
	</cffunction>

	<cffunction name="getRedemptionsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="operationMode" type="string" required="true">
		<cfargument name="reportFileName" type="string" required="false">

		<cfset var local = structNew()>

		<cfif arguments.operationMode eq 'grid'>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"mActive.lastName + mActive.firstName + mActive.memberNumber #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"t.transactionDate #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"c.couponCode #arguments.event.getValue('orderDir')#, t.amount, t.detail")>
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRedemptions" result="local.qryRedemptionsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID INT, @orgID INT, @totalCount INT, @fDateFrom DATE, @fDateTo DATETIME, @fCouponCode VARCHAR(200),	
					@fAmtFrom DECIMAL(14,2), @fAmtTo DECIMAL(14,2), @fAssociatedMemberID INT, @fAssociatedGroupID INT;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				
				<cfif arguments.event.getValue('fDateFrom','') NEQ ''>	
					SET @fDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('fDateFrom')#">;	
				</cfif>
				<cfif arguments.event.getValue('fDateTo','') NEQ ''>	
					SET @fDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('fDateTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fCouponCode','') NEQ ''>
					SET @fCouponCode = replace(<cfqueryparam value="#arguments.event.getTrimValue('fCouponCode')#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.event.getTrimValue('fAmtFrom','') NEQ ''>
					SET @fAmtFrom = <cfqueryparam value="#abs(rereplace(arguments.event.getTrimValue('fAmtFrom',0),'[^\d.]+','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">;
				</cfif>
				<cfif arguments.event.getTrimValue('fAmtTo','') NEQ ''>
					SET @fAmtTo = <cfqueryparam value="#abs(rereplace(arguments.event.getTrimValue('fAmtTo',0),'[^\d.]+','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">;
				</cfif>
				<cfif arguments.event.getValue('fAssociatedMemberID',0) gt 0>
					SET @fAssociatedMemberID = <cfqueryparam value="#arguments.event.getValue('fAssociatedMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fAssociatedGroupID',0) gt 0>
					SET @fAssociatedGroupID = <cfqueryparam value="#arguments.event.getValue('fAssociatedGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>

				IF OBJECT_ID('tempdb..##tblRedemptionSearch') IS NOT NULL 
					DROP TABLE ##tblRedemptionSearch;
				CREATE TABLE ##tblRedemptionSearch (transactionDiscountID int PRIMARY KEY);

				INSERT INTO ##tblRedemptionSearch (transactionDiscountID)
				SELECT DISTINCT td.autoID
				FROM dbo.tr_transactionDiscounts AS td
				INNER JOIN dbo.tr_coupons c ON c.siteID = @siteID and c.couponID = td.couponID AND td.isActive = 1
					<cfif arguments.event.getTrimValue('fCouponCode','') NEQ ''>
						AND c.couponCode = @fCouponCode
					</cfif>
				INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID AND t.transactionID = td.transactionID AND t.statusID = 1
				INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = t.assignedToMemberID
				INNER JOIN dbo.ams_members AS mActive ON mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID
					<cfif arguments.event.getValue('fAssociatedMemberID',0) gt 0>
						AND mActive.memberID = @fAssociatedMemberID
					</cfif>
				<cfif arguments.event.getValue('fAssociatedGroupID',0) gt 0>
					INNER JOIN dbo.cache_members_groups mg ON mg.orgID = @orgID AND mg.memberID = mactive.memberID AND mg.groupid = @fAssociatedGroupID
				</cfif>
				WHERE td.orgID = @orgID
				AND td.isActive = 1
				<cfif arguments.event.getValue('fDateFrom','') NEQ '' and arguments.event.getValue('fDateTo','') NEQ ''>
					AND t.transactionDate BETWEEN @fDateFrom AND @fDateTo
				</cfif>
				<cfif arguments.event.getTrimValue('fAmtFrom','') NEQ ''>
					AND ISNULL(t.amount,0) >= @fAmtFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fAmtTo','') NEQ ''>
					AND ISNULL(t.amount,0) <= @fAmtTo
				</cfif>
				<cfif arguments.event.getValue('fUsage','') eq "ev">
					and td.itemType = 'EventRate'
				<cfelseif arguments.event.getValue('fUsage','') eq "sub">
					and td.itemType = 'Subscriber'
				<cfelseif arguments.event.getValue('fUsage','') eq "sto">
					and td.itemType = 'StoreOrder'
				<cfelseif arguments.event.getValue('fUsage','') eq "sw">
					and td.itemType in ('SWBRate','SWLRate','SWODRate','SWTLRate')
				</cfif>;

				<cfif arguments.operationMode eq 'grid'>
					IF OBJECT_ID('tempdb..##tblRedemptions') IS NOT NULL 
						DROP TABLE ##tblRedemptions;
					CREATE TABLE ##tblRedemptions (transactionID INT, transactionDate DATE, firstName VARCHAR(75), lastName VARCHAR(75), 
						memberNumber VARCHAR(50), memberID INT, company VARCHAR(200), couponID INT, couponCode VARCHAR(15), 
						amount DECIMAL(18,2), detail VARCHAR(max), itemType varchar(30), glAccountID int, row INT);

					INSERT INTO ##tblRedemptions
					SELECT DISTINCT t.transactionID, t.transactionDate, mActive.firstName, mActive.lastName, mActive.memberNumber, 
						mActive.memberID, mActive.company, td.couponID, c.couponCode, t.amount, t.detail, td.itemType, t.debitGlAccountID, 
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) AS row
					FROM ##tblRedemptionSearch AS tmp
					INNER JOIN dbo.tr_transactionDiscounts AS td ON td.orgID = @orgID and td.autoID = tmp.transactionDiscountID
					INNER JOIN dbo.tr_coupons AS c ON c.siteID = @siteID and c.couponID = td.couponID
					INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID AND t.transactionID = td.transactionID AND t.statusID = 1
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = t.assignedToMemberID
					INNER JOIN dbo.ams_members AS mActive ON mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID;

					SELECT @totalCount = @@ROWCOUNT;
				
					DECLARE @posStart INT, @posStartAndCount INT;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
								
					WITH allGLS as (
						select rgl.GLAccountID, gl.accountCode, rgl.thePathExpanded
						from dbo.fn_getRecursiveGLAccounts(@orgID) as rgl
						inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.glaccountID = rgl.glaccountID
						where gl.status <> 'D'
					)
					SELECT tmp.transactionID, tmp.transactionDate, tmp.firstName, tmp.lastName, tmp.memberNumber, tmp.memberID, tmp.company, 
						tmp.couponID, tmp.couponCode, tmp.amount, tmp.detail, gl.thePathExpanded, gl.accountCode, 
						case 
						when tmp.itemType = 'EventRate' then 'Events'
						when tmp.itemType = 'Subscriber' then 'Subscriptions'
						when tmp.itemType = 'StoreOrder' then 'Store'
						when tmp.itemType in ('SWBRate','SWLRate','SWODRate','SWTLRate') then 'SeminarWeb'
						end as usageType, @totalCount as totalCount
					FROM ##tblRedemptions AS tmp
					INNER JOIN allGLS as gl on gl.GlAccountID = tmp.GLAccountID
					WHERE tmp.row > @posStart
					AND tmp.row <= @posStartAndCount
					ORDER BY row;

					IF OBJECT_ID('tempdb..##tblRedemptions') IS NOT NULL 
						DROP TABLE ##tblRedemptions;
				<cfelseif arguments.operationMode eq 'export'>
					IF OBJECT_ID('tempdb..##tblRedemptions') IS NOT NULL 
						DROP TABLE ##tblRedemptions;
					CREATE TABLE ##tblRedemptions (Redeemed datetime, FirstName VARCHAR(75), LastName VARCHAR(75), MemberNumber VARCHAR(50),
						Company VARCHAR(200), Coupon VARCHAR(15), Savings DECIMAL(18,2), Detail VARCHAR(max), UsageArea varchar(13), 
						AccountName VARCHAR(MAX), AccountCode varchar(200));

					WITH allGLS as (
						select rgl.GLAccountID, gl.accountCode, rgl.thePathExpanded
						from dbo.fn_getRecursiveGLAccounts(@orgID) as rgl
						inner join dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.glaccountID = rgl.glaccountID
						where gl.status <> 'D'
					)
					INSERT INTO ##tblRedemptions (Redeemed, FirstName, LastName, MemberNumber, Company, Coupon, Savings, Detail, UsageArea, 
						AccountName, AccountCode)
					SELECT t.transactionDate, mActive.firstName, mActive.lastName, mActive.memberNumber, mActive.company, c.couponCode, t.amount, 
						t.detail, case 
						when td.itemType = 'EventRate' then 'Events'
						when td.itemType = 'Subscriber' then 'Subscriptions'
						when td.itemType = 'StoreOrder' then 'Store'
						when td.itemType in ('SWBRate','SWLRate','SWODRate','SWTLRate') then 'SeminarWeb'
						end, gl.thePathExpanded, gl.accountCode
					FROM ##tblRedemptionSearch as tmp
					INNER JOIN dbo.tr_transactionDiscounts AS td on td.orgID = @orgID and td.autoID = tmp.transactionDiscountID
					INNER JOIN dbo.tr_coupons AS c ON c.siteID = @siteID and c.couponID = td.couponID 
					INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID AND t.transactionID = td.transactionID AND t.statusID = 1
					INNER JOIN allGLS as gl on gl.GlAccountID = t.debitGLAccountID
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = t.assignedToMemberID
					INNER JOIN dbo.ams_members AS mActive ON mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID;

					DECLARE @selectsql VARCHAR(MAX);
					SET @selectsql = 'SELECT Redeemed, FirstName, LastName, MemberNumber, Company, Coupon, Savings, UsageArea, Detail, 
						AccountName, AccountCode, ROW_NUMBER() OVER(ORDER BY Redeemed DESC) AS mcCSVorder
						*FROM* ##tblRedemptions';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.reportFileName#', @returnColumns=1;

					IF OBJECT_ID('tempdb..##tblRedemptions') IS NOT NULL 
						DROP TABLE ##tblRedemptions;
				</cfif>

				IF OBJECT_ID('tempdb..##tblRedemptionSearch') IS NOT NULL 
					DROP TABLE ##tblRedemptionSearch;				

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryRedemptions>
	</cffunction>

</cfcomponent>