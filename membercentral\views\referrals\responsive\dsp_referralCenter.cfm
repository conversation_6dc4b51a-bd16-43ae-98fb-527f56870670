﻿<cfset  variables.structData=attributes.data>
<cfsavecontent variable="variables.gridJS">
	<cfoutput>	
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/dhtmlxgrid/dhtmlxgrid.css" />
	<link rel="stylesheet" href="/assets/common/javascript/intl-tel-input/18.1.1/css/intlTelInput.css">
 	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/memberCentral_grid.js"></script>
	<script type='text/javascript' src='/assets/common/javascript/common.js'></script>
	<script type="text/javascript">dhtmlxError.catchError("LoadXML", mcg_ErrorHandler);</script>	
	<script src="/assets/common/javascript/intl-tel-input/18.1.1/js/intlTelInput.min.js"></script>	
	<style type="text/css">
	.boxName { width:795px; height:17px; margin-bottom:-10px; padding-top:10px; padding-left:3px; border:1px solid ##707070; background-color:##ddd; }
	.gridBox { width:800px; height:250px; margin-top:6px; margin-bottom:10px; }
	.gridNavigation { width:800px; background-color:##dddddd; margin-top:3px; margin-bottom:3px; border:1px solid ##707070; }
	.gridRNumMember { margin:14px 0 0 0; }
	##boxes { padding: 5px 0 15px 20px; }
	.saveButton { padding: 0 10px 0 700px; }
	fieldset.panelBorder {  border: 1px solid ##d6d6d6;  padding: 0 20px 10px; margin: 0 0 11px 0; width: 760px;}
	legend.panelBorder { width:auto; padding:0 10px;border-bottom:none; margin-bottom:0px; }
	.sortColumn{cursor:pointer;}
	##sw_listing{border: 1px solid ##cccccc;margin-top: 20px;padding: 15px 20px;}
	@media screen and (min-width: 766px){
		.ReferralDate{text-align:right;}
		.dataResp{display:none;}
		.dataTable{display:block;}
		.loadingGrid{display:block!important;}
	}
	@media screen and (max-width: 765px){
		.ReferralDate{text-align:left;}
		.dataResp{display:block;}
		.dataTable{display:none !important;}
		.loadingGrid{display:none!important;}
	}
	##sw_tabs li a{color:##08c !important;}
	##sw_tabs li.active a{color:##fff !important;}
	##addArea .popover-title{background-color: ##FFF;border-bottom: 0px;}
	.MCReferralBtnBox ul{margin:0;}
	.MCReferralBtnBox li{list-type:none;display:inline;}
	.MCReferralBtnBox li a{margin: 7px;}
	.MCReferralBtnBoxMob li a{margin: 10px 0px !important;}
	.MCReferralBtnBox button.dropdown-toggle{background: transparent;border: none;box-shadow: none;}
	.MCReferralBtnBox button.dropdown-toggle{background: transparent;border: none;box-shadow: none;}
	.MCIssueDescBox{position:absolute;top:0px;background:white;border:1px solid ##ccc;padding:5px;z-index:999999; }
	.show-read-more .more-text{display: none;}
	.mb-10{margin-bottom:10px;}
	.mt-30{margin-top:30px;}
	.text-right{text-align:right !important;}
	</style>
	<script>
		function editReferral(rID){
			parent.location.href = "#variables.structData.mainurl#&ra=editReferral&clientReferralID=" + rID;
		}
		function editCase(cID){
			parent.location.href = "#variables.structData.mainurl#&ra=editCase&clientReferralID=" + cID;
		}
		function applyPayment(rID){
			parent.location.href = "#variables.structData.mainurl#&ra=editCase&applyPayment=1&clientReferralID=" + rID + "##feesInfo";
		}
		function sendStatement(rID) {
			var windowWidth = $(window).width();
			var _popupWidth = 550;
			if(windowWidth < 585) {
				_popupWidth = windowWidth - 30;
			} 
			$.colorbox( {innerWidth:_popupWidth, innerHeight:400, href:'#variables.structData.link.viewCaseStatement#&clientReferralID=' + rID, iframe:true, overlayClose:false} );
		}
		function closeBox() { $.colorbox.close(); }		
		function checkMaxPanel(){
			<cfif val(variables.structData.maxNumberOfPanels) gt 0>
				var memPanelCheckBox = $('form[name="memberPanelForm"] input[type="checkbox"].memberPanelCheckBox');
				var count = 0;
				$.each(memPanelCheckBox, function(index,value){
					if($(value).is(':checked')){
					count = count + 1; 
					}
				});
				if(count > #val(variables.structData.maxNumberOfPanels)#){
					alert('You can select a maximum of #val(variables.structData.maxNumberOfPanels)# panel(s).');
					return false;
				}
			<cfelse>
				return true;
			</cfif>
		}
		function showHideSubPanels(pID) {
			$('##panelParent_'+pID).toggle();
			if(!$("input[name='panelID_" + pID + "']").is(":checked")) {
				$('##panelParent_'+pID).find('input[type=checkbox]:checked').removeAttr('checked');
			}
		}
		function printReport(){
			$('##btnPrint').attr('disabled','disabled');
			$('##btnPrint').text($('##btnPrint').attr('data-temptext'));

			$.ajax({
				type: "POST",
				url: '#variables.structData.mainurl#&ra=printMonthlyReport&mode=stream',
				success: successPrint,
				dataType: 'JSON'
			});
		}
		function successPrint(r){
			var content = r['HTMLCONTENT'];
			var pri = document.getElementById("contenttoprint").contentWindow;
			pri.document.open();
			pri.document.write(content);
			pri.document.close();
			pri.focus();
			pri.print();
			$('[data-toggle="popover"]').popover('hide');
			$('##btnPrint').removeAttr('disabled');
		}
		function closeReferral(_clientrefId){
			$.colorbox( {innerWidth:500,innerHeight:250, href:'/?event=cms.showResource&resID=#variables.structData.siteResourceID#&ra=selectReferralStatus&grid=1&clientReferralID='+_clientrefId+'&mode=direct', iframe:true, overlayClose:false} );
		}
		function retainReferral(_clientrefId){
			$.colorbox( {innerWidth:500,innerHeight:250, href:'/?event=cms.showResource&resID=#variables.structData.siteResourceID#&ra=selectCase&grid=1&clientReferralID='+ _clientrefId + '&taskCode=retainCase&mode=direct', iframe:true, overlayClose:false} );
		}
		$(function() {
			$(window).on("resize load", function() {
				var windowWidth = $(window).width();
				if(windowWidth < 585) {
					$.colorbox.resize({innerWidth:windowWidth-30,innerHeight:330});
				} else{
					$.colorbox.resize({innerWidth:550, innerHeight:330});
				}
			});
		});
		$(document).ready(function(){
			$(document).on('click','##btnDownload',function(e){
				e.preventDefault();
				$(this).attr('disabled','disabled');
				$(this).text($(this).attr('data-temptext'));
				window.location.href = $(this).attr('href');
				setTimeout(function(){
					$('[data-toggle="popover"]').popover('hide');
					$('##btnDownload').removeAttr('disabled');
				},500);
				
			});
			
			$('.MCIssueDesc').on('hover',function(){
				$(this).children('.MCIssueDescBox').toggle();
			});
			$(document).on('click','.closeReferralBtn',function(){
				var _clientrefId = $(this).data('clientrefid');

				var msg = '<div class="m-3"><h3>Close Referral</h3><p style="margin: 15px 0;padding: 0;">Are you sure you would like to close this referral?</p><br/><div class="mt-3 text-center"><button class="btn btn-sm btn-default" type="button" onClick="closeReferral('+_clientrefId +');">Yes</button> <button class="btn btn-sm btn-default" type="button" onClick="top.closeBox();">No</button></div></div>';

				$.colorbox( {innerWidth:500,innerHeight:250, html:msg, iframe:false, overlayClose:false} );
			});
			$(document).on('click','.retainBtn',function(){
				var _clientrefId = $(this).data('clientrefid');

				var msg = '<div class="m-3"><h3>Retain Referral</h3><p style="margin: 15px 0;padding: 0;">Are you sure you would like to retain this referral?</p><br/><div class="mt-3 text-center"><button class="btn btn-sm btn-default" type="button" onClick="retainReferral('+_clientrefId +');">Yes</button> <button class="btn btn-sm btn-default" type="button" onClick="top.closeBox();">No</button></div></div>';

				$.colorbox( {innerWidth:500,innerHeight:250,onCleanup:closeBox, html:msg, overlayClose:false} );
			});
			var maxLength = 97;
			$(".show-read-more").each(function(){
				var myStr = $(this).text();
				if($.trim(myStr).length > maxLength){
					var newStr = myStr.substring(0, maxLength);
					var removedStr = myStr.substring(maxLength, $.trim(myStr).length);
					$(this).empty().html(newStr);
					$(this).append(' <a href="javascript:void(0);" class="read-more">read more...</a>');
					$(this).append('<span class="more-text">' + removedStr + '</span>');
				}
			});
			$(document).on('click','.read-more',function(){
				$(this).siblings(".more-text").contents().unwrap();
				$(this).remove();
			});
		});
	</script>
	<cfset local.redirectBaseUrl = '/?pg=referrals&panel=browse&tab=#variables.structData.tabToShow#'>
	<cfset local.redirectUrl = ""/>
	<cfif structKeyExists(variables.structData, "refDateRanges") and variables.structData.refDateRanges GT 0>
		<cfset local.redirectUrl = local.redirectUrl & '&refDateRange=#variables.structData.refDateRanges#'>
	</cfif>
	<cfif structKeyExists(variables.structData, "filterReferralID") and len(variables.structData.filterReferralID)>
		<cfset local.redirectUrl = local.redirectUrl & '&filterReferralID=#variables.structData.filterReferralID#'>
	</cfif>
	<cfif structKeyExists(variables.structData, "filterClientLastName") and len(variables.structData.filterClientLastName)>
		<cfset local.redirectUrl = local.redirectUrl & '&filterClientLastName=#variables.structData.filterClientLastName#'>
	</cfif>
	<cfif structKeyExists(variables.structData, "filterClientFirstName") and len(variables.structData.filterClientFirstName)>
		<cfset local.redirectUrl = local.redirectUrl & '&filterClientFirstName=#variables.structData.filterClientFirstName#'>
	</cfif>
	<cfif structKeyExists(variables.structData, "filterStatus") and variables.structData.filterStatus GT 0>
		<cfset local.redirectUrl = local.redirectUrl & '&filterStatus=#variables.structData.filterStatus#'>
	</cfif>
	<cfif structKeyExists(variables.structData, "filterfeesDue") and len(variables.structData.filterfeesDue)>
		<cfset local.redirectUrl = local.redirectUrl & '&filterfeesDue=#variables.structData.filterfeesDue#'>
	</cfif>
	<cfif structKeyExists(variables.structData, "filterAmountDue") and len(variables.structData.filterAmountDue)>
		<cfset local.redirectUrl = local.redirectUrl & '&filterAmountDue=#variables.structData.filterAmountDue#'>
	</cfif>
	
	<cfset local.paginationRedirectUrl = local.redirectBaseUrl & local.redirectUrl>
	<cfif structKeyExists(variables.structData, "sort") and len(variables.structData.sort)>
		<cfset local.paginationRedirectUrl = local.paginationRedirectUrl & '&sort=#variables.structData.sort#'>
		<cfif structKeyExists(variables.structData, "orderBy") and len(variables.structData.orderBy)>
			<cfset local.paginationRedirectUrl = local.paginationRedirectUrl & '&orderBy=#variables.structData.orderBy#'>
		</cfif>
	</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(variables.gridJS)#" />

<cfoutput>
<section class="content innerContent" style="margin-bottom: 20px;">
	<div class="container">
		<div class="inner-main">
			<div class="container-fluid"> 
				<cfif variables.structData.qryReferralSettings.dspMonthReportLink >
					<div class="row-fluid ">	
						<div id="addArea" style="text-align:right;padding-right:100px;" class="fr  span12">							
							<cfsavecontent variable="local.popoverContent"><div class="text-center"><div class="btn-group"><a class="btn" id="btnDownload" data-orgtext="Download PDF" data-temptext="processing..." href="<cfoutput>#variables.structData.mainurl#&ra=downloadMonthlyReport</cfoutput>">Download PDF</a><button data-orgtext="Print Report" data-temptext="processing..." class="btn" id="btnPrint" type="button" onclick="printReport();">Print Report</button></div></div></cfsavecontent>
							<a href="##" data-toggle="popover">Print Monthly Report</a>
						</div>
					</div>
					<script>$(document).ready(function(){$('[data-toggle="popover"]').popover({title: "",placement:'bottom',html:true,animation:false,content:'#local.popoverContent#'}); });</script>
					<iframe id="contenttoprint" style="height: 0px; width: 0px; position: absolute;" frameborder="0"></iframe>
				</cfif>
				
				<div id="sw_tabset2">
					<ul class="nav nav-pills" id="sw_tabs">
					
						<li id="nav_0"  <cfif variables.structData.tabToShow eq 'referral'>  class="active" </cfif> ><a href="/?pg=referrals&panel=browse&tab=referral">My Referrals</a></li>
					
						<li id="nav_1" <cfif variables.structData.tabToShow eq 'mrc'>  class="active" </cfif>><a href="/?pg=referrals&panel=browse&tab=mrc">My Retained Cases</a></li>
					
						<li id="nav_2" <cfif variables.structData.tabToShow eq 'mrch'>  class="active" </cfif>><a href="/?pg=referrals&panel=browse&tab=mrch">My Referral/Case History</a></li>
						<cfif variables.structData.dspPanelList>
							<li id="nav_3" <cfif variables.structData.tabToShow eq 'panel'>  class="active" </cfif>><a href="/?pg=referrals&panel=browse&tab=panel">Panels</a></li>
						</cfif>
						<li id="nav_2" <cfif variables.structData.tabToShow eq 'rfp'>  class="active" </cfif>><a href="/?pg=referrals&panel=browse&tab=rfp">My Outstanding Fees</a></li>
						
						<cfif variables.structData.referralsSMS eq 1 AND variables.structData.feEnableTextMessagingMember eq 1>
							<li id="nav_4" <cfif variables.structData.tabToShow eq 'rfSMS'>  class="active" </cfif>><a href="/?pg=referrals&panel=browse&tab=rfSMS">Text Messaging</a></li>
						</cfif>
					</ul>
				</div>
				<div id="sw_listing" class="contentBox">
					<cfswitch expression="#variables.structData.tabToShow#">
						<cfcase value="referral">
							<cfinclude template="dsp_referralCenter_myReferrals.cfm">
						</cfcase>
						<cfcase value="mrc">
							<cfinclude template="dsp_referralCenter_myRetainedCases.cfm">
						</cfcase>
						<cfcase value="mrch">
							<cfinclude template="dsp_referralCenter_myReferralCaseHistory.cfm">
						</cfcase>
						<cfcase value="panel">
							<cfinclude template="dsp_referralCenter_panels.cfm">
						</cfcase>
						<cfcase value="rfp">
							<cfinclude template="dsp_referralCenter_myOutstandingFees.cfm">
						</cfcase>
						<cfcase value="rfSMS">
							<cfinclude template="dsp_referralCenter_textMessaging.cfm">
						</cfcase>
					</cfswitch>
				</div>
			</div>
		</div>
	</div>
</section>
<script>

	function removeParam(key, sourceURL) {
		var rtn = sourceURL.split("?")[0],
			param,
			params_arr = [],
			queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
		if (queryString !== "") {
			params_arr = queryString.split("&");
			for (var i = params_arr.length - 1; i >= 0; i -= 1) {
				param = params_arr[i].split("=")[0];
				if (param === key) {
					params_arr.splice(i, 1);
				}
			}
			rtn = rtn + "?" + params_arr.join("&");
		}
		return rtn;
	}

	function paginationChange(){
		$('.goToPage').on('click',function(event){	
			event.preventDefault();
			var changeLink = $(this).parent().find(".nextPageLink");
			if(changeLink.length == 0){
				var changeLink = $(this).parent().find(".prevPageLink");	
			}
			var start = $.trim($(this).parent().find("[name='goToPage']").val());
			if($.isNumeric(start) && parseInt(start) && parseInt(start) > 0 && parseInt(start) <= parseInt($(this).parent().find(".goToPage").attr("totalpages"))){
				var start = parseInt(start);
				changeLink.attr('start',start);
				changeLink.trigger("click");
			}else{
				$(this).parent().find("[name='goToPage']").val('');
			}		
		});
		$('.paginationChange').on('click',function(event){
			event.preventDefault();
			if($(".refFilterForm [name='start']").length){
				$(".refFilterForm [name='start']").val($(this).attr("start"));
			}
			$('.refFilterForm').trigger('submit',["pagination"]);
			
		});

	}
	function manageCheckAll(){
		if($('.checkAll').length > 0){
			if($('.checkAll').is(':checked')){
				$('.checkAll').trigger('change');
			}
		}
	}

	function applyGeneralChange(currentpage,totalpages,orderby,sort){
		var previousPage = "";
		var nextPage = "";
		var goToPage = "";

		if(currentpage != 1){ 
			previousPage = '<a style="text-decoration:underline;" href="javascript:void(0)" start="' + (currentpage-1) + '" class="paginationChange prevPageLink">Previous Page</a>';
		}

		if(currentpage != totalpages){
			nextPage = '&nbsp;<a style="text-decoration:underline;"  href="javascript:void(0)" start="' + (currentpage+1) + '" class="paginationChange nextPageLink">Next Page</a>';
		}
		if(totalpages > 1){
			goToPage = '&nbsp;<input type="text" name="goToPage" class="input-small" value="" style="margin-bottom: 5px; text-align: center; width: 27px; padding: 3px;">&nbsp;<a href="##" style="text-decoration:underline;" totalpages="'+totalpages+'" class="goToPage">Go To Page</a>';
		}
		
		var pageValue = currentpage + " of " + totalpages + " pages  &nbsp;" + previousPage + nextPage + goToPage;

		$(".sortColumn[data-sort='" + sort + "']").attr('data-order',orderby);
		$(".paginationValue").html(pageValue);
		$('.paginationChange').unbind("click");
		paginationChange();
		$(".paginationHolder").removeClass('hide');
		$(".paginationHolder").show();
	}
	function setRefTotal(){
		arrCheckedClientReferrals = [];
		totaldue = parseFloat('00.00');
		$('.lgScreen .checkRef:checked').each(function(){
			totaldue += parseFloat($(this).data('dues'));
			clientRefId  = $(this).data('crid');
			arrCheckedClientReferrals.push(clientRefId);
		});
		$('span##currentTotal').html('$'+parseFloat(totaldue).toFixed(2));
		
		if($('.lgScreen .checkRef:checked').length != $('.lgScreen .checkRef').length){
			$('##checkedAll').val(0);
			$('.checkAll').removeAttr('checked');
		}
	}
	function continuePayForm(payUrl){
		if(totaldue > 0){
			let arrReq = [];
			let stateIDForTax = $('##stateIDForTax').val();
			let zipForTax = $('##zipForTax').val();

			if (stateIDForTax == '') arrReq[arrReq.length] = 'Billing State/Province is required.';
			if (zipForTax == '') arrReq[arrReq.length] = 'Billing Postal Code is required.';
			
			if (stateIDForTax > 0 && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDForTax,''))
				arrReq[arrReq.length] = 'Invalid Billing Postal Code.';

			if (arrReq.length) {
				alert(arrReq.join('\n'));
				return false;
			}

			var _redirectUrlTemp = "#local.redirectBaseUrl#";
			_redirectUrlTemp = removeParam('refDateRange',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterReferralID',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterClientLastName',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterClientFirstName',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterStatus',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterAmountDue',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterfeesDue',_redirectUrlTemp);		
			_redirectUrlTemp = removeParam('tab',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('start',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('count',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('sort',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('orderBy',_redirectUrlTemp);		
			
			var objParams = {};

			var currentTab = $.trim($(this).find("[name='tab']").val());
			var filterReferralID = $.trim($(this).find("[name='filterReferralID']").val());
			var filterClientLastName = $.trim($(this).find("[name='filterClientLastName']").val());
			var filterClientFirstName = $.trim($(this).find("[name='filterClientFirstName']").val());
			var filterStatus = $.trim($(this).find("[name='filterStatus']").val());
			var refDateRange = $.trim($(this).find("[name='refDateRange']").val());
			var sort = $.trim($(this).find("[name='sort']").val());	
			var count = $.trim($(this).find("[name='count']").val());	
			var orderBy = $.trim($(this).find("[name='orderBy']").val());	
			var start = $.trim($(this).find("[name='start']").val());		
			
			if(currentTab != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&tab=' + currentTab;
			}
			objParams.start = 1;
			if(sort != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&sort=' + sort;
				objParams.sort = sort;
			}
			if(count != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&count=' + count;
				objParams.count = count;
			}
			if(orderBy != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&orderBy=' + orderBy;
				objParams.orderBy = orderBy;
			}
			if(refDateRange != 0){
				_redirectUrlTemp = _redirectUrlTemp + '&refDateRange=' + refDateRange;
				objParams.refDateRange = refDateRange;
			}
			if(filterReferralID != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&filterReferralID=' + filterReferralID;
				objParams.filterReferralID = filterReferralID;
			}
			if(filterClientLastName != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&filterClientLastName=' + filterClientLastName;
				objParams.filterClientLastName = filterClientLastName;
			}
			if(filterClientFirstName != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&filterClientFirstName=' + filterClientFirstName;
				objParams.filterClientFirstName = filterClientFirstName;
			}
			if(filterStatus != 0){
				_redirectUrlTemp = _redirectUrlTemp + '&filterStatus=' + filterStatus;
				objParams.filterStatus = filterStatus;
			}
			objParams.checkedReferral = arrCheckedClientReferrals.toString();
			objParams.stateIDForTax = stateIDForTax;
			objParams.zipForTax = zipForTax;
			$('.step1').hide();
			$('##billingInfoContainer').remove();
			$('.paymentWrap').html('Please Wait...').load(payUrl,objParams);	
		}
	}

	$(document).ready(function(){
		
		paginationChange();		

		$('.sortColumn').on('click',function(){		
			var _this = $(this);
			var _sortColumn = $(_this).attr("data-sort");
			var _sortOrder = $(_this).attr("data-order");

			if(_sortOrder == "asc"){
				_sortOrder = "desc";
			}else{
				_sortOrder = "asc";
			}
			if($("[name='sort']").length){
				$("[name='sort']").val(_sortColumn);
			}
			if($("[name='orderBy']").length){
				$("[name='orderBy']").val(_sortOrder);
			}
			$('.refFilterForm').trigger('submit',["sort"]);
		});		
		
		$('.filterClear').on('click',function(){
			$(this).parents('form')[0].reset();
			$('.refFilterForm').trigger('submit');
		});

		$(".refFilterForm").submit(function( event, from="search"){			
			event.preventDefault();
			if(from == "search"){
				$('.filterRefferals').attr("disabled", true);
			}
			$('.loadingGrid').html('<i class="icon-spin icon-spinner icon-1x"></i>&nbsp;loading...');
			var _redirectUrlTemp = "#local.redirectBaseUrl#";
			_redirectUrlTemp = removeParam('refDateRange',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterReferralID',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterClientLastName',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterClientFirstName',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterStatus',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterAmountDue',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('filterfeesDue',_redirectUrlTemp);		
			_redirectUrlTemp = removeParam('tab',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('start',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('count',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('sort',_redirectUrlTemp);
			_redirectUrlTemp = removeParam('orderBy',_redirectUrlTemp);		
			
			var objParams = {};

			var currentTab = $.trim($(this).find("[name='tab']").val());
			var filterReferralID = $.trim($(this).find("[name='filterReferralID']").val());
			var filterClientLastName = $.trim($(this).find("[name='filterClientLastName']").val());
			var filterClientFirstName = $.trim($(this).find("[name='filterClientFirstName']").val());
			var filterStatus = $.trim($(this).find("[name='filterStatus']").val());
			var refDateRange = $.trim($(this).find("[name='refDateRange']").val());
			var sort = $.trim($(this).find("[name='sort']").val());	
			var count = $.trim($(this).find("[name='count']").val());	
			var orderBy = $.trim($(this).find("[name='orderBy']").val());	
			var start = $.trim($(this).find("[name='start']").val());		
			
			if(currentTab != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&tab=' + currentTab;
			}
			if(from != "search" && start != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&start=' + start;
				objParams.start = start;
			}
			if(sort != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&sort=' + sort;
				objParams.sort = sort;
			}
			if(count != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&count=' + count;
				objParams.count = count;
			}
			if(orderBy != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&orderBy=' + orderBy;
				objParams.orderBy = orderBy;
			}
			if(refDateRange != 0){
				_redirectUrlTemp = _redirectUrlTemp + '&refDateRange=' + refDateRange;
				objParams.refDateRange = refDateRange;
			}
			if(filterReferralID != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&filterReferralID=' + filterReferralID;
				objParams.filterReferralID = filterReferralID;
			}
			if(filterClientLastName != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&filterClientLastName=' + filterClientLastName;
				objParams.filterClientLastName = filterClientLastName;
			}
			if(filterClientFirstName != ""){
				_redirectUrlTemp = _redirectUrlTemp + '&filterClientFirstName=' + filterClientFirstName;
				objParams.filterClientFirstName = filterClientFirstName;
			}
			if(filterStatus != 0){
				_redirectUrlTemp = _redirectUrlTemp + '&filterStatus=' + filterStatus;
				objParams.filterStatus = filterStatus;
			}	
			if(currentTab == "referral"){
				var referralData	= function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						var data = r.result;
						
						var gridData = r.result.data;
						var gridHtml = "";
						var gridMobileHtml = "";
						if(gridData.length > 0){
							$.each(gridData, function( index, obj ) {								
									gridHtml = gridHtml + '<tr class="gridData"><td width="140" style="text-align:left;"><a href="javascript:editReferral(' + obj.clientreferralid + ')" target="_self" title="View/Edit Referral"><b>' + obj.clientreferralid + '</b></a></td>'+
									'<td width="175" style="text-align:left;">' + obj.clientname + '</td>'+
									'<td width="175" style="text-align:left;">' + obj.issuedesc + '</td>'+
									'<td width="250" style="text-align:left;">' + obj.statusname + ((!obj.isClosed)? ('<div class="MCReferralBtnBox">'+
												'<ul><li><a href="javascript:void(0);" class="retainBtn btn"  data-clientrefid="' + obj.clientreferralid + '">Retain Referral</a></li><li><a href="javascript:void(0);" class="closeReferralBtn btn" data-clientrefid="' + obj.clientreferralid + '">Close Referral</a></li></ul></div>'):'') + '</td><td width="175" style="text-align:left;">' + obj.clientreferraldate + '</td></tr>';
                                    
									gridMobileHtml = gridMobileHtml + '<div class="row-fluid" bgcolor="##DEDEDE">'+
										'<div class="span12 clearfix well well-small">'+
											'<div class="row-fluid">'+
												'<div class="span8 eventLeft list-text">'+		
													'<p><a href="javascript:editReferral(' + obj.clientreferralid + ')" target="_self" title="View/Edit Referral"><b>Referral ## ' + obj.clientreferralid + '</b></a></p>'+
													'<p><b>Client Name :</b>  ' + obj.clientname + '</p>'+													
													'<p><b>Description :</b> ' + obj.issuedesc + '</p>'+
													'<p><b>Status :</b> ' + obj.statusname + '</p>'+													
												'</div>'+											
												'<div class="span4 eventRight">'+
													'<p class="ReferralDate"><b>Referral Date :</b> ' + obj.clientreferraldate + '</p>'+
												'</div>'+
											'</div>'+											
										'</div>'+
									'</div>';
							});
							applyGeneralChange(data.currentpage,data.totalpages,data.orderby,data.sort);
							$(".noReferrals").addClass('hide');
						}else{
							$(".noReferrals").removeClass('hide');
							$(".paginationHolder").hide();
						}
						
						$(".gridData").remove();	
						$(".gridHead").after(gridHtml);
						$(".dataResp").html(gridMobileHtml);
						$('.filterRefferals').removeAttr("disabled");
						$('.loadingGrid').html('&nbsp;');						
						
					} else {
						window.location.href = _redirectUrlTemp;
					}
				};
				TS_AJX('REFERRALS','getMemberReferralsGridData',objParams,referralData,referralData,10000,referralData);
			}	
			if(currentTab == "mrc"){
				var filterfeesDue = $.trim($(this).find("[name='filterfeesDue']").val());
				if(filterfeesDue != ""){
					_redirectUrlTemp = _redirectUrlTemp + '&filterfeesDue=' + filterfeesDue;
					objParams.filterfeesDue = filterfeesDue;
				}
				var referralData	= function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						var data = r.result;
						
						var gridData = r.result.data;
						var gridHtml = "";
						var gridMobileHtml = "";
						if(gridData.length > 0){
							$.each(gridData, function( index, obj ) {
								
								var applyHtml = "";
								if(parseInt(obj.totalfeesdue) > 0){
									applyHtml = '<a href="javascript:applyPayment(' + obj.clientreferralid + ')" title="Apply Payment to this Case" style="margin-right:4px;"><i class="icon-money"></i></a>&nbsp;';
								}else{
									applyHtml = '<a href="javascript:void(0);" class="invisible" style="margin-right:4px;"><i class="icon-money"></i></a>&nbsp;';
								}	
								gridHtml = gridHtml + '<tr class="gridData"><td width="140" style="text-align:left;"><a href="javascript:editCase(' + obj.clientreferralid + ')" target="_self" title="View/Edit Referral"><b>' + obj.clientreferralid + '</b></a></td>'+
											'<td width="175" style="text-align:left;">' + obj.clientname + '</td>' +
											'<td width="190" style="text-align:left;">' + obj.clientreferraldate + '</td>'+
											'<td width="190" style="text-align:left;">$' + formatCurrency(obj.collectedfeetotal) + '</td>'+
											<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
												'<td width="130" style="text-align:left;">$' + formatCurrency(obj.referralduestotal) + '</td>'+
												'<td width="130" style="text-align:left;">$' + formatCurrency(obj.totalclientfee) + '</td>'+
												'<td width="130" style="text-align:left;">$' + formatCurrency(obj.totalfeesdue) + '</td>'+
												'<td width="135" style="text-align:left;">$' + formatCurrency(obj.totalfeespaid) + '</td>'+
											<cfelse>
												'<td width="175" style="text-align:left;">$' + formatCurrency(obj.referralduestotal) + '</td>'+
												'<td width="175" style="text-align:left;">$' + formatCurrency(obj.totalfeesdue) + '</td>'+
												'<td width="175" style="text-align:left;">$' + formatCurrency(obj.totalfeespaid) + '</td>'+
											</cfif>
											
											'<td width="175" style="text-align:left;">' + applyHtml + '<a href="javascript:sendStatement(' + obj.clientreferralid + ')" title="View and E-mail Statement" ><i class="icon-envelope"></i></a></td></tr>';
								gridMobileHtml = gridMobileHtml + '<div class="row-fluid" bgcolor="##DEDEDE">'+
										'<div class="span12 clearfix well well-small">'+
											'<div class="row-fluid">'+
												'<div class="span8 eventLeft list-text">'+			
													'<p><a href="javascript:editCase(' + obj.clientreferralid + ')" target="_self" title="View/Edit Referral"><b>Referral ## ' + obj.clientreferralid + '</b></a></p>'+
													'<p><b>Client Name :</b>  ' + obj.clientname + '</p>'+
													'<p><b>Referral Date :</b> ' + obj.clientreferraldate + '</p>'+
													'<p><b>Collected from Client to Date :</b> $' + formatCurrency(obj.collectedfeetotal) + '</p>'+
													'<p><b>Referral Fees :</b> $' + formatCurrency(obj.referralduestotal) + '</p>'+
													<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
														'<p><b>Client Fees :</b> $' + formatCurrency(obj.totalclientfee) + '</p>'+
													</cfif>
													'<p><b>Total Fees Due :</b> $' + formatCurrency(obj.totalfeesdue) + '</p>'+
													'<p><b>Fees Paid to Date :</b> $' + formatCurrency(obj.totalfeespaid) + '</p>'+													
												'</div>'+												
												'<div class="span4" style="text-align:right">' + applyHtml + '<a href="javascript:sendStatement(' + obj.clientreferralid + ')" title="View and E-mail Statement" ><i class="icon-envelope"></i></a></div>'+
											'</div>'+											
										'</div>'+
									'</div>';
							});
							applyGeneralChange(data.currentpage,data.totalpages,data.orderby,data.sort);
							$(".noReferrals").addClass('hide');
						}else{
							$(".noReferrals").removeClass('hide');
							$(".paginationHolder").hide();
						}

						$(".gridData").remove();	
						$(".gridHead").after(gridHtml);
						$(".dataResp").html(gridMobileHtml);						
						$('.filterRefferals').removeAttr("disabled");	
						$('.loadingGrid').html('&nbsp;');					
						
					} else {
						window.location.href = _redirectUrlTemp;
					}
				};
				TS_AJX('REFERRALS','getMemberCasesGridData',objParams,referralData,referralData,10000,referralData);				
			}	
			if(currentTab == "mrch"){
				var filterAmountDue = $.trim($(this).find("[name='filterAmountDue']").val());
				if(filterAmountDue != ""){
					_redirectUrlTemp = _redirectUrlTemp + '&filterAmountDue=' + filterAmountDue;
					objParams.filterAmountDue = filterAmountDue;
				}

				var referralData	= function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						var data = r.result;
						
						var gridData = r.result.data;
						var gridHtml = "";
						var gridMobileHtml = "";
						if(gridData.length > 0){
							$.each(gridData, function( index, obj ) {
								
								var editHtml = "";
								var editMobHtml = "";
								var iscase = "";
								var hasPendingDue = "No";
								if(obj.iscase){
									editHtml = '<a href="javascript:editCase(' + obj.clientreferralid + ')" target="_self" title="View/Edit Case"><b>' + obj.clientreferralid + '</b></a>';
									editMobHtml = '<a href="javascript:editCase(' + obj.clientreferralid + ')" target="_self" title="View/Edit Case"><b>Referral ## ' + obj.clientreferralid + '</b></a>';
									iscase = "Yes";
								}else{
									editHtml = '<a href="javascript:editReferral(' + obj.clientreferralid + ')" target="_self" title="View/Edit Referral"><b>' + obj.clientreferralid + '</b></a>';
									editMobHtml = '<a href="javascript:editReferral(' + obj.clientreferralid + ')" target="_self" title="View/Edit Referral"><b>Referral ## ' + obj.clientreferralid + '</b></a>';
									iscase = "No";
								}
								if(obj.duepending != 'null' && obj.duepending != '' && obj.duepending > 0){
									hasPendingDue = "Yes";
								}
								
								gridHtml = gridHtml + '<tr class="gridData"><td width="140" style="text-align:left;">' + editHtml + '</td>'+
											'<td width="175" style="text-align:left;">' + obj.clientname + '</td>'+
											'<td width="175" style="text-align:left;">' + obj.issuedesc + '</td>'+
											'<td width="190" style="text-align:left;">' + obj.statusname + '</td>'+
											'<td width="175" style="text-align:left;">' + iscase + '</td>'+
											'<td width="175" style="text-align:left;">' + hasPendingDue + '</td>'+
											'<td width="175" style="text-align:left;">' + obj.clientreferraldate + '</td></tr>';

								gridMobileHtml = gridMobileHtml + '<div class="row-fluid" bgcolor="##DEDEDE">'+
										'<div class="span12 clearfix well well-small">'+
											'<div class="row-fluid">'+
												'<div class="span8 eventLeft list-text">'+		
													'<p>' + editMobHtml +'</p>'+
													'<p><b>Client Name :</b>  ' + obj.clientname + '</p>'+												
													'<p><b>Description :</b> ' + obj.issuedesc + '</p>'+
													'<p><b>Status :</b> ' + obj.statusname + '</p>'+
													'<p><b>Is Retained Case? :</b> ' + iscase + '</p>'+	
													'<p><b>Is Amount Due? :</b> ' + hasPendingDue + '</p>'+																									
												'</div>'+											
												'<div class="span4 eventRight">'+
													'<p class="ReferralDate"><b>Referral Date :</b> ' + obj.clientreferraldate + '</p>'+
												'</div>'+
											'</div>'+											
										'</div>'+
									'</div>';	
							});
							applyGeneralChange(data.currentpage,data.totalpages,data.orderby,data.sort);
							$(".noReferrals").addClass('hide');
						}else{
							$(".noReferrals").removeClass('hide');
							$(".paginationHolder").hide();
						}						
						
						$(".gridData").remove();	
						$(".gridHead").after(gridHtml);	
						$(".dataResp").html(gridMobileHtml);					
						$('.filterRefferals').removeAttr("disabled");	
						$('.loadingGrid').html('&nbsp;');					
						
					} else {
						window.location.href = _redirectUrlTemp;
					}
				};
				TS_AJX('REFERRALS','getMemberReferralHistoryGridData',objParams,referralData,referralData,10000,referralData);
			}
			if(currentTab == "rfp"){
				var referralData	= function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						var data = r.result;
						
						var gridData = r.result.data;
						var gridHtml = "";
						var gridMobileHtml = "";
						if(gridData.length > 0){
							$.each(gridData, function( index, obj ) {	
								strCol1 = '<td style="text-align:left;">';
								strCol1 += '<input type="checkbox" name="checkRef" class="checkRef" data-dues="'+obj.totalFeesDue+'" data-crid="'+obj.clientReferralID+'" id="checkRef'+obj.clientReferralID+'">';
								strCol1 += '</td>';														
								gridHtml = gridHtml + '<tr class="gridData lgScreen">'+strCol1+'<td  style="text-align:left;"><a href="javascript:'+(obj.caseID > 0 ? 'editCase' : 'editReferral')+'(' + obj.clientReferralID + ')" target="_self" title="View/Edit Referral"><b>' + obj.clientReferralID + '</b></a></td>'+
								'<td  style="text-align:left;">' + obj.clientName + '</td>'+
								'<td  style="text-align:left;">' + obj.clientReferralDate + '</td><td style="text-align:left;">' + obj.statusName +  '</td>'+
								<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
									'<td style="text-align:left;">$' + formatCurrency(obj.clientFeeDue) +  '</td>' +
									'<td style="text-align:left;">$' + formatCurrency(obj.amtToBePaidTotal) +  '</td>' +
									'<td style="text-align:left;">$' + formatCurrency(obj.totalFeesDue) +  '</td></tr>';
								<cfelse>
									'<td style="text-align:left;">$' + formatCurrency(obj.amtToBePaidTotal) +  '</td></tr>';
								</cfif>
								
								gridMobileHtml = gridMobileHtml + '<div class="row-fluid" bgcolor="##DEDEDE">'+
									'<div class="span12 clearfix well well-small">'+
										'<div class="row-fluid">'+
											'<div class="span8 eventLeft list-text">'+
												'<p><input type="checkbox" name="checkRef" class="checkRef" data-dues="'+obj.totalFeesDue+'" data-crid="'+obj.clientReferralID+'" id="checkRef'+obj.clientReferralID+'"></p>'+		
												'<p><a href="javascript:'+(obj.caseID > 0 ? 'editCase' : 'editReferral')+'(' + obj.clientReferralID + ')" target="_self" title="View/Edit Referral"><b>Referral ## ' + obj.clientReferralID + '</b></a></p>'+
												'<p><b>Client Name :</b>  ' + obj.clientName + '</p>'+													
												'<p><b>	Referral Date :</b> ' + obj.clientReferralDate + '</p>'+
												'<p><b>Status :</b> ' + obj.statusName + '</p>'+	
												<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
													'<p><b>Client Fees :</b> $' + formatCurrency(obj.clientFeeDue) + '</p>'+	
												</cfif>
												'<p><b>Referral Fees :</b> $' + formatCurrency(obj.amtToBePaidTotal) + '</p>'+
												<cfif variables.structData.qryReferralSettings.collectClientFeeFE>
													'<p><b>Total Fees Due :</b> $' + formatCurrency(obj.totalFeesDue) + '</p>'+	
												</cfif>	
											'</div>'+
										'</div>'+											
									'</div>'+
								'</div>';
							});
							applyGeneralChange(data.currentpage,data.totalpages,data.orderby,data.sort);
							$(".noReferrals").addClass('hide');
						}else{
							$(".noReferrals").removeClass('hide');
							$(".paginationHolder").hide();
						}
						
						$(".gridData").remove();	
						$(".gridHead").after(gridHtml);
						$(".dataResp").html(gridMobileHtml);
						$('.filterRefferals').removeAttr("disabled");
						$('.loadingGrid').html('&nbsp;');								
						manageCheckAll();
						setRefTotal();
					} else {
						window.location.href = _redirectUrlTemp;
					}
				};
				TS_AJX('REFERRALS','getMemberRFPGridData',objParams,referralData,referralData,10000,referralData);				
			}
		});

		$(".refFilterForm input").keypress(function(event) {
			if (event.which == 13) {
				event.preventDefault();
				$(this).parents('form').submit();
			}
		});	
		$(".dataTable").removeClass('hide');
		$(".dataTable").show();	
		
		arrCheckedClientReferrals = [];
		totaldue = 0;
		$(document).on('change','.checkRef',function(){				
			vCrid = $(this).data('crid');
			if($(this).is(':checked')){
				$('input[data-crid="'+vCrid+'"]').each(function(){
					$(this).attr('checked','checked');
				});
			}else{
				$('input[data-crid="'+vCrid+'"]').each(function(){
					$(this).removeAttr('checked');
				});
			}
			setRefTotal();
		});
		$(document).on('click','##paymentBtn',function(){
			payUrl = '#variables.structData.MAINURLPAY#';
			continuePayForm(payUrl);
		});
		$(document).on('change','.checkAll',function(){
			if($(this).is(':checked')){
				$('.checkRef').attr('checked','checked');
				$('##checkedAll').val(1);
			}else{
				$('.checkRef').removeAttr('checked')
				$('##checkedAll').val(0);
				$('span##currentTotal').html('$00.00');
			}
			
			setRefTotal();
		});	
		
	});
</script>
</cfoutput>