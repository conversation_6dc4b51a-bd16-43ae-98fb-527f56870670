<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
<cfset local.siteTimeZoneID = application.objSiteInfo.getSiteInfo(arguments.strCredit.creditOrgcode).defaultTimeZoneID>
<cfset local.siteTimeZoneCode = local.objTSTZ.getTZCodeFromTZID(timeZoneID=local.siteTimeZoneID)>
<cfset local.siteTimeZoneAbbr = local.objTSTZ.getTZFromTZID(timeZoneID=local.siteTimeZoneID)>

<cfset local.completionDateInSiteTimezone = arguments.enrollmentInfo.dateCompleted>
<cfif local.siteTimeZoneCode neq "US/Central">
    <cfset local.completionDateInSiteTimezone = local.objTSTZ.convertTimeZone(
        dateToConvert=arguments.enrollmentInfo.dateCompleted,
        fromTimeZone='US/Central',
        toTimeZone=local.siteTimeZoneCode
    )>
</cfif>

<cfoutput>
<html>
<head>
<title>Uniform Certificate of Attendance</title>
<style>
* { padding: 0; margin: 0; }
html, body {
    width: 8.5in;
    font-family: <PERSON><PERSON>ri, sans-serif;
    font-size: 12pt;
    color: ##000;
    padding: 0;
    margin: 0;
}
.certificate-wrapper {
    margin: 0.5in;
    width: 7.5in;
    padding: 0;
}
@page {
    size: 8.5in 11in;
    margin: 0.5in;
}
.header {
    text-align: center;
    margin-bottom: 10px;
}
.title {
    font-weight: bold;
    font-size: 14pt;
    margin-bottom: 10px;
	margin-top: 10px;
}
.section {
    margin-bottom: 5px;
}
.field-group {
    margin-bottom: 5px;
	padding-left: 220px;
}
.underline {
    border-bottom: 1px solid ##000;
    display: inline-block;
    width: 100%;
	padding-left: 10px;
}
.signature-field {
    display: inline-block;
    width: 100%;
    margin-right: 5%;
    border-bottom: 1px solid ##000;
    height: 30px;
    margin-bottom: 5px;
}
</style>
</head>
<body>

<div class="certificate-wrapper">
<div class="header">
    <div class="title">Uniform Certificate of Attendance</div>
    <div>To be filed within fifteen (15) days upon completion of the program to The Office<br/>
    of Professional Programs at 501 Woodlane St., Suite 303, Little Rock, AR<br/>
    72201-1026 or e-<NAME_EMAIL>.</div>
</div>

<div class="section">
    <strong>Sponsoring Organization:</strong> Arkansas Bar Association
</div>

<div class="section">
    <strong>Activity Title:</strong> #arguments.enrollmentInfo.contentName#
</div>

<div class="section">
    <strong>Date:</strong> #DateFormat(local.completionDateInSiteTimezone,"mmmm d, yyyy")# at #TimeFormat(local.completionDateInSiteTimezone,"h:mm tt")# #local.siteTimeZoneAbbr#
</div>

<div class="section">
    <strong>Location:</strong> Self-Paced Online
</div>
<cfif len(arguments.strCredit.courseApproval)>
	<div class="section">
	    <strong>Course Number:</strong> #arguments.strCredit.courseApproval#
	</div>
</cfif>

<div class="section">
   This program is eligible for a total of:
    
    <cfset local.generalCredits = 0>
    <cfset local.ethicsCredits = 0>
    <cfset local.adLitemDRCredits = 0>
    <cfset local.adLitemDNCredits = 0>
    
    <cfif arguments.strCredit.qryWddxCredits.recordcount>
        <cfloop query="arguments.strCredit.qryWddxCredits">
            <cfset local.creditValue = val(arguments.strCredit.qryWddxCredits.numCredits)>
            <cfif local.creditValue eq 0>
                <cfset local.creditValue = 0>
            </cfif>
            <cfif findNoCase("general", arguments.strCredit.qryWddxCredits.fieldName)>
                <cfset local.generalCredits = local.creditValue>
            <cfelseif findNoCase("ethics", arguments.strCredit.qryWddxCredits.fieldName)>
                <cfset local.ethicsCredits = local.creditValue>
            <cfelseif findNoCase("AdLitemDR", arguments.strCredit.qryWddxCredits.fieldName)>
                <cfset local.adLitemDRCredits = local.creditValue>
            <cfelseif findNoCase("AdLitemDN", arguments.strCredit.qryWddxCredits.fieldName)>
                <cfset local.adLitemDNCredits = local.creditValue>
            </cfif>
        </cfloop>
    </cfif>
    
    <div class="field-group">
        <span>
            <cfif local.generalCredits gt 0>#NumberFormat(local.generalCredits, "0.0")#<cfelse>0.0</cfif>
        </span>
        General credits based on a full 60-minute hour
    </div>
    
    <div class="field-group">
        <span>
            <cfif local.ethicsCredits gt 0>#NumberFormat(local.ethicsCredits, "0.0")#<cfelse>0.0</cfif>
        </span>
        Ethics credits based on a full 60-minute hour
    </div>
    
    <div class="field-group">
        <span>
            <cfif local.adLitemDRCredits gt 0>#NumberFormat(local.adLitemDRCredits, "0.0")#<cfelse>0.0</cfif>
        </span>
        Ad Litem/DR credits based on a full 60-minute hour
    </div>
    
    <div class="field-group">
        <span>
            <cfif local.adLitemDNCredits gt 0>#NumberFormat(local.adLitemDNCredits, "0.0")#<cfelse>0.0</cfif>
        </span>
        Ad Litem/DN credits based on a full 60-minute hour
    </div>
</div>

<strong>NOTE:</strong> Arkansas is a 60-minute CLE state and all programs will be reviewed based on a full 
60-minute. Introductory remarks, keynote addresses, business meetings, breaks 
and meals (with no speaker or presenter), receptions, etc., are not included in the 
computation of credit.

<div class="title">TO BE COMPLETED BY ATTORNEY:</div>

<p>By signing below, I certify that I attended the activity described above and am entitled to claim 
<cfif local.generalCredits gt 0>#NumberFormat(local.generalCredits, "0.0")#<cfelse>0.0</cfif>
 General credit hours, 

<cfif local.ethicsCredits gt 0>#NumberFormat(local.ethicsCredits, "0.0")#<cfelse>0.0</cfif>
 Ethics credits, 

<cfif local.adLitemDRCredits gt 0>#NumberFormat(local.adLitemDRCredits, "0.0")#<cfelse>0.0</cfif>
 Ad Litem/DR credit and/or 

<cfif local.adLitemDNCredits gt 0>#NumberFormat(local.adLitemDNCredits, "0.0")#<cfelse>0.0</cfif>
 Ad Litem/DN credits.</p>

<div style="display: inline-block; width: 35%; vertical-align: top;">
    <div class="underline">#arguments.enrollmentInfo.fullname#</div>
    <div style="padding-bottom: 5px;">Attorney Name (Print):</div>
    <div class="signature-field"></div>
    <div style="padding-bottom: 5px;">Attorney Signature</div>
</div>

<div style="display: inline-block; width: 50%; margin-left: 2%; vertical-align: top;">
	<div class="underline">#arguments.strCredit.idNumber#</div>
    <div style="padding-bottom: 5px; white-space: nowrap;">Membership, Registration or Supreme Court Number</div>
    <div class="signature-field"></div>
    <div style="padding-bottom: 5px;">Date</div>
</div>

<div style="padding: 5px; width: 50%;">
	<div class="signature-field"></div>
    <div>State where credits are to be registered</div>
</div>
<strong>Note:</strong> Complete a certificate for each state in which you are required to file. Rules for CLE in some states 
    require the provider to file attendance with the regulator as a service to lawyers. Please confirm 
    jurisdictional reporting requirements with the provider or state regulator.

<div style="text-align: left; padding-left: 400px;">
	<strong>Acknowledged by:</strong><br/>
	<img src="#local.assetsSiteURL#kfrye_signature.jpeg" style="max-height: 40px;" />
	<div style="border-bottom: 1px solid ##000; width: 150px;"></div>
	<strong>Sponsor Representative</strong>
</div>

</div>

</body>
</html>
</cfoutput>
