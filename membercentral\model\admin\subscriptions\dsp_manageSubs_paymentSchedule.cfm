<cfoutput>
<div id="paySchedData" data-numpaymentstouse='#local.numPaymentsToUse#' data-firstpaymentminimum='#local.firstPaymentMinimum#' data-amounttocharge='#local.amountToCharge#' data-invprofilescount='#local.subAmts.qryInvProfiles.recordcount#'></div>
<script id="paySchedData-arrPaySchedule" type="application/json">
	#serializeJSON(local.arrPaySchedule)#
</script>
<script id="paySchedData-sPayOrder" type="application/json">
	#serializeJSON(local.subAmts.payOrderArray)#
</script>
<div class="alert alert-info mb-2">
	NOTE: <strong>Saving this subscription as Billed will not retain a modified Payment Schedule.</strong> Only modify the Payment Schedule if you intend to Accept this subscription by choosing Finalize & Invoice.
</div>
<input type="hidden" id="ps_upfront_amt" name="ps_upfront_amt" value="#local.firstPaymentMinimum#">
<input type="hidden" id="schedFormLoaded" name="schedFormLoaded" value="1">
<input type="hidden" id="hInvoiceProfileIDs" name="hInvoiceProfileIDs" value="#valueList(local.subAmts.qryInvProfiles.invoiceProfileID)#">
<table id="paymentScheduleTable" class="table table-sm table-borderless">
	<tr>
		<td width="5"></td>
		<td class="font-weight-bold">Invoice Due Date</td>
		<td width="5"></td>
		<td class="font-weight-bold">Amount</td>
		<td style="width:100px;"></td>
	</tr>
	<tbody id="tblPaySchRows">
	</tbody>
	<tr>
		<td colspan="5">
			<div class="d-flex align-items-center flex-wrap mt-2 mb-3">
				<div class="mx-1">Pay over:</div>
				<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(1);">1 month</button>
				<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(3);">3 months</button>
				<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(4);">4 months</button>
				<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(6);">6 months</button>
				<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(9);">9 months</button>
				<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(12);">12 months</button>
				<cfif local.maxFrequencyInstallments gt 12>
					<button type="button" class="btn btn-sm btn-secondary mx-1 px-2 py-1" onclick="quickLinkSpread(#local.maxFrequencyInstallments#);">#local.maxFrequencyInstallments# months</a>
				</cfif>
			</div>
		</td>
	</tr>
	<tr id="paymentTotalsRow">
		<td width="5"></td>
		<td colspan="2" class="align-left font-weight-bold">Total:</td>
		<td class="text-right font-weight-bold" id="paymentTotalAmt">#dollarformat(local.amountToCharge)#</td>
		<td></td>
	</tr>
	<tr id="paymentScheduledRow">
		<td width="5"></td>
		<td colspan="2" class="align-left font-weight-bold">Scheduled:</td>
		<td class="text-right font-weight-bold" id="amtScheduled">#dollarformat(local.amountToCharge)#</td>
		<td></td>
	</tr>
</table>

<script id="mc_subPaymentScheduleRowHTML" type="text/x-handlebars-template">
	<tr id="payScheduleRow{{rowid}}" class="payScheduleRow" data-rowid="{{rowid}}">
		<td width="5" class="text-right schedRowNumDisplay"></td>
		<td>
			<div class="input-group input-group-sm">
				<input type="text" name="ps_{{rowid}}_date" id="ps_{{rowid}}_date" class="form-control form-control-sm dateControl paymentScheduleDateInput" value="{{date}}" autocomplete="off">
				<div class="input-group-append">
					<span class="input-group-text cursor-pointer calendar-button" data-target="ps_{{rowid}}_date"><i class="fa-solid fa-calendar"></i></span>
				</div>
			</div>
		</td>
		<td width="5"></td>
		<td>
			<div class="input-group input-group-sm">
				<div class="input-group-prepend">
					<span class="input-group-text">$</span>
				</div>
				<input class="form-control form-control-sm payScheduleAmt" type="text" name="ps_{{rowid}}_amt" id="ps_{{rowid}}_amt" autocomplete="off" value="{{amt}}" onchange="checkSpread();">
			</div>
		</td>
		<td>
			{{##compare rowid '>' 1}}	
				<a href="javascript:removePayScheduleDateRow({{rowid}});" title="remove"><i class="fa-solid fa-circle-xmark text-danger"></i></a>	
			{{/compare}}
		</td>
	</tr>
	<cfoutput query="local.subAmts.qryInvProfiles">
		<tr id="payScheduleInvProfRow{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#" class="payScheduleInvProfRow payScheduleInvProfRow{{rowid}}">
			<td width="5"></td>
			<td colspan="2" class="pt-0 align-left" title="#local.subAmts.qryInvProfiles.invoiceProfileName#">
				<span class="font-italic small">#Left(local.subAmts.qryInvProfiles.invoiceProfileName, 20)#</span>
			</td>
			<td class="pt-0 text-right">
				<input type="hidden" id="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_amt" name="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_amt">
				<input type="hidden" id="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_appearOnThisInvoice" name="hps_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_appearOnThisInvoice" value="false">
				<span id="span_{{rowid}}_#local.subAmts.qryInvProfiles.invoiceProfileID#_amt" class="small"></span>
			</td>
			<td></td>
		</tr>
	</cfoutput>
</script>
</cfoutput>