USE seminarWeb
GO

DECLARE @CSALinkID INT;

SELECT @CSALinkID = csa.CSALinkID 
FROM tblCreditSponsorsAndAuthorities csa 
INNER JOIN tblCreditSponsors AS cs ON cs.sponsorID = csa.sponsorID
INNER JOIN tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID
WHERE cs.orgCode = 'ARKBAR'
AND ca.authorityName = 'Arkansas Continuing Legal Education Board';

IF @CSALinkID IS NOT NULL
BEGIN
    UPDATE dbo.tblCreditSponsorsAndAuthorities 
    SET swodGrantedCreditCert = 'swod_arkbar_grantedcredit'
    WHERE CSALinkID = @CSALinkID;
END
GO
