<cfsavecontent variable="local.headCode">
	<cfoutput>
	<script language="javascript" src="/assets/common/javascript/date.js"></script>
	<script language="javascript">
		const #toScript(local.rootSubscriptionID,"mcsub_rootsubid")#
		const #toScript(local.memberID,"mcsub_mid")#
		const #toScript(local.freeRateDisplay,"mcsub_freeratedisplay")#
		const #toScript(local.maxFrequencyInstallments,"mcsub_maxFreqInstallments")#
		let mcsub_rootsubratefreqid = 0;
		let subAddOnsCache;
		let subTermDatesCache;
		let payScheduleRowsCache;
		let addonsLoaded = false;
		let arrPaySchedule = [];
		let numPaymentsToUse = 0;
		let firstPaymentMinimum = 0;
		let sPayOrder = [];
		let amountToCharge = 0;
		let invProfilesCount = 0;
		let reloadPayScheduleForm = true;
		let hasModifiedPaySchedule = false;
		let todayDate = '#dateFormat(now(), "M/d/yyyy")#';

		var strTermDates = {
			"rootSub": {
				"subID": mcsub_rootsubid,
				"rfid": 0,
				"subStartDate": '',
				"subEndDate": '',
				"graceEndDate": '',
				"recogStartDate": '',
				"recogEndDate": ''
			}
		};
		function getDisplayPrice(price,honorFreeRateDisplay){
			let priceDisplay = '';
			price = Number(parseFloat(price).toFixed(2));
			if (price > 0) {
				priceDisplay = '$' + formatCurrency(price);
			} else {
				priceDisplay = honorFreeRateDisplay && mcsub_freeratedisplay.length ? mcsub_freeratedisplay : '$' + formatCurrency(price);
			}
			return priceDisplay;
		}
		function onBlurEditableRateAmt(thisObj) {
			let thisRatePrice = formatCurrency($(thisObj).val());
			$(thisObj).val(thisRatePrice);
			updateSubCardSummaryTotals();
		}
		function validateSchedAmount(val) {
			if (typeof val === "string") {
				return val === val.trim() && !isNaN(val) && isFinite(val);
			}
			return typeof val === "number" && isFinite(val);
		}
		function initSummaryStep(){
			$("form##frmManageSubs .btnFinals").off('click').on("click", function() {
				setConfirmMetaData($(this).val() || '');
				return true;
			});
			$('##fTermFromRO').val($('##fTermFrom').val());
			$('##fTermToRO').val($('##fTermTo').val());
			mca_setupDatePickerField('fTermGracePrompt',$('##fTermTo').val());
			mca_setupDatePickerField('fOfferExpDatePrompt','#DateFormat(now(),"m/d/yyyy")#');
			$('##subSummaryTable').removeClass('d-none');
			$('##summaryStepActions').removeClass('d-none');
			loadPaymentScheduleForm();
		}
		function saveExtraDatesPrompt(){
			var fTermGracePrompt = $('##fTermGracePrompt').val() || '';
			$('##fTermGrace').val(fTermGracePrompt);
			var fOfferExpDatePrompt = $('##fOfferExpDatePrompt').val() || '';
			$('##fOfferExpDate').val(fOfferExpDatePrompt);

			$('##extraDatesPromptConfirm').val(1);
			toggleExtraDatesPromptForm(false);
			onSubmitFinalForm();
		}
		function cancelExtraDatesPrompt(){
			toggleExtraDatesPromptForm(false);
		}
		function toggleExtraDatesPromptForm(f){
			if(f){
				var skipEmail = $('##skipEmailTemplateNotifications').val();
				var createAsBilled = $('##chkBilled').val();
				var proceedButtonText = (createAsBilled == 1 ? 'Create Subscription as Billed' : (skipEmail == 1 ? 'Finalize and Invoice - Skip Automated Emails' : 'Finalize and Invoice - Allow Automated Emails *'));
				$('button##extraDatesPromptProceedBtn').html(proceedButtonText);

				var fTermGrace = $('##fTermGrace').val() || '';
				var hasTermGraceDate = fTermGrace.length ? true : false;
				$('##graceEndPrompFieldLabel').toggleClass('text-grey',hasTermGraceDate);
				$('##fTermGracePrompt').val(fTermGrace).prop('readonly', hasTermGraceDate).prop('disabled', hasTermGraceDate);

				$('##fOfferExpDate,##fOfferExpDatePrompt').val('');
				$('##offerExpDateWrapper').toggleClass('d-none', createAsBilled != 1);

				var alertMsg = '';
				if (!hasTermGraceDate && createAsBilled == 1) {
					alertMsg = '<div>We recommend that all subscriptions have a Grace End Date. Additionally, for Billed Subscriptions, we recommend setting an Offer Expiration Date. <b>Confirm the Grace End Date and Offer Expiration Date below.</b></div>';
				} else if (!hasTermGraceDate) {
					alertMsg = '<div>We recommend that all subscriptions have a Grace End Date. <b>Confirm the Grace End Date below.</b></div>';
				} else if (createAsBilled == 1) {
					alertMsg = '<div>We recommend that all Billed Subscriptions have an Offer Expiration Date. <b>Confirm the Offer Expiration Date below.</b></div>';
				}
				if (alertMsg.length)
					$('##extraDatesFormAlerts').html(alertMsg);
			}
			$('##extraDatesPromptForm').toggleClass('d-none',!f);
			$('##finalButtonsContainer').toggleClass('d-none',f);
		}
		function setConfirmMetaData(opt){
			$('##skipEmailTemplateNotifications').val(opt == 'skipEmail' ? 1 : 0);
			$('##chkBilled').val(opt == 'createAsBilled' ? 1 : 0);
		}
		function onSubmitFinalForm(){
			$('##subSaveCard button.btnFinals').prop('disabled',true);

			setTimeout(function() {
				validateFinalForm().then(() => {
					var createAsBilled = $('##chkBilled').val();
					if((createAsBilled == 1 || $('##fTermGrace').val() == '') && $('##extraDatesPromptConfirm').val() == 0){
						toggleExtraDatesPromptForm(true);
						$('button.btnFinals').prop('disabled',false);
						return false;
					}
					setPayScheduleRowIDs();
					if($('##payScheduleRowIDsList').val() == ''){
						alert('Issue with Payment Schedule Setup');
						$('button.btnFinals').prop('disabled',false);
						return false;
					}
					showFinalSubmitProcess();
					document.forms["frmManageSubs"].submit();
				})
				.catch(error => {
					setTimeout(function() { $('button.btnFinals').prop('disabled',false); }, 100);
				});
			}, 100);

			return false;
		}
		function validateFinalForm() {
			mca_hideAlert('err_finalsubform');
			return new Promise((resolve, reject) => {
				var arrReq = [];
				var schedFormLoaded = $('##schedFormLoaded').val() || 0;
				if(schedFormLoaded == 0){
					arrReq.push('Payment Schedule is not defined.');
				}
				else {
					arrReq = getPaymentScheduleFormErrors();
					arrReq = arrReq.map(item => item + ' (Payment Schedule)');
					arrReq = arrReq.concat(getAdditionalBillingInfoErrors());
				}

				if(arrReq.length == 0)
					resolve();
				else {
					mca_showAlert('err_finalsubform', arrReq.join('<br/>'));
					reject();
				}
			});
		}
		function showFinalSubmitProcess(){
			$('##subsSummaryContainer ##summaryStepActions button').prop('disabled',true);
			$('##finalSaveLoadingCard').removeClass('d-none');
		}
		function toggleFinalButtons(f){
			$('##subSaveCard button.btnFinals').prop('disabled',!f);
		}
		function changePrice(){
			toggleChangePriceForm(true);
		}
		function toggleChangePriceForm(f){
			$('.subSummarySection').addClass('d-none');
			$('##subSummaryTable').removeClass('d-none'); /* show summary */
			$('##subSummaryTable .rateRow .subRateDisplayElm').toggleClass('d-none',f);
			$('##subSummaryTable .rateRow .subRateEditableElm').toggleClass('d-none',!f);
			/*$('##subSummaryTable .freqDisplayElm').toggleClass('d-none',f);*/
			$('##summaryStepActions').toggleClass('d-none',f);
			$('##changePriceFormActions').toggleClass('d-none',!f);
			toggleFinalButtons(!f);
			$("html, body").animate({scrollTop: 0}, 750);
		}
		function confirmPriceChange(){
			/* no more frequency display once price change is saved, and only term price (modified) will be shown */
			$('##subSummaryTable .freqDisplayElm').addClass('d-none');

			$('##subSummaryTable input.modifiedRatePriceField').each(function() {
				let linkedDisplayFldID = $(this).data('linkeddisplayfieldid');
				let thisVal = $(this).val().replace(',','');
				$('##'+linkedDisplayFldID).html(getDisplayPrice(thisVal,true));
				$(this).data('originalvalue',thisVal);
			});

			toggleChangePriceForm(false);
			if(hasModifiedPaySchedule){
				$('##payScheduleResetMsg').html('<span class="text-warning ml-1">Your changes to the payment schedule have been cleared.</span>').show().delay(1000).fadeOut(5000);
			}
			loadPaymentScheduleForm();
		}
		function cancelPriceChange(){
			$('##subSummaryTable input.modifiedRatePriceField').each(function() {
				$(this).val($(this).data('originalvalue')).blur();
			});
			refreshAmountDueTodayFromSchedule();
			toggleChangePriceForm(false);
		}
		function setPayScheduleRowsCache(){
			payScheduleRowsCache = $('##paymentScheduleListing tbody##tblPaySchRows').clone();
		}
		function setPaymentSchedule(){
			togglePaymentScheduleForm(true);
			if(reloadPayScheduleForm){
				loadPaymentScheduleForm();
			}
			else {
				setPayScheduleRowsCache();
			}
		}
		function loadPaymentScheduleForm(){
			$('##multipleInvProfilesAlert').addClass('d-none');
			$('##subsSummaryContainer ##paymentScheduleListing').html('');
			var onLoadComplete = function(responseText, textStatus, xhr){
				if (textStatus !== 'success') {
					console.log('Payment Schedule Form Load Failed: ' + textStatus);
					alert('An error occured while loading payment schedule form.');
					toggleFinalButtons(false);
					return;
				}
				
				numPaymentsToUse = $('##paySchedData').data('numpaymentstouse');
				firstPaymentMinimum = parseFloat($('##paySchedData').data('firstpaymentminimum'));
				amountToCharge = $('##paySchedData').data('amounttocharge');
				invProfilesCount = $('##paySchedData').data('invprofilescount');

				try {
					arrPaySchedule = JSON.parse($('##paySchedData-arrPaySchedule').html());
					sPayOrder = JSON.parse($('##paySchedData-sPayOrder').html());
				} catch (e) {
					console.error("Invalid JSON in paySchedData");
				}

				initPaymentScheduleForm();
				setPayScheduleRowsCache();
				reloadPayScheduleForm = false;
				hasModifiedPaySchedule = false;
			};
			var fd = $('##frmManageSubs').serializeArray();
			$('##subsSummaryContainer ##paymentScheduleListing')
				.html($('##subLoadingCard').html())
				.load('#local.loadPaymentScheduleFormLink#', fd, onLoadComplete);
		}
		function togglePaymentScheduleForm(f){
			$('.subSummarySection').addClass('d-none');
			if(!f){
				$('##subSummaryTable').removeClass('d-none'); /* show summary */
			}
			$('##paymentScheduleForm').toggleClass('d-none',!f);
			$('##summaryStepActions').toggleClass('d-none',f);
			$('##paymentScheduleFormActions').toggleClass('d-none',!f);
			toggleFinalButtons(!f);
			$("html, body").animate({scrollTop: 0}, 750);
		}
		function confirmPaymentSchedule(){
			var arrReq = getPaymentScheduleFormErrors();
			if(arrReq.length){
				mca_showAlert('err_schedform', arrReq.join('<br/>'));
			}
			else {
				togglePaymentScheduleForm(false);
				refreshAmountDueTodayFromSchedule();
				hasModifiedPaySchedule = true;
			}	
		}
		function cancelPaymentSchedule(){
			if (typeof payScheduleRowsCache == "object"){
				/* resets form */
				mca_hideAlert('err_schedform');
				$('##paymentScheduleListing tbody##tblPaySchRows').replaceWith(payScheduleRowsCache.clone());
				getActiveSchedRowsSelectorArray().each(function () {
					var thisRowID = $(this).data('rowid');
					mca_setupDatePickerField('ps_'+thisRowID+'_date');
				});
				mca_setupCalendarIcons('frmManageSubs');
				var scheduledSum = getTotalScheduledAmount();
				$('##amtScheduled').html('$' + formatCurrency(scheduledSum));
				$('##amtScheduled').removeClass('text-danger');
				checkMaxScheduleLimitReached();
			}
			togglePaymentScheduleForm(false);
		}
		function refreshAmountDueTodayFromSchedule(){
			let amtDueToday = 0;
			$('input.paymentScheduleDateInput').each(function() {
				const thisDateVal = $(this).val().trim();
				const thisRow = $(this).closest('tr');
				const thisAmt = parseFloat(thisRow.find('input.payScheduleAmt').val() || 0);

				if (thisDateVal !== '' && thisDateVal === todayDate && !thisRow.hasClass('deletedRow') && thisAmt > 0) {
					amtDueToday += thisAmt;
				}
			});

			let discountPerInstallmentTotal = 0;
			$('##frmManageSubs .subCouponRateFields').each(function() {
				let thisSubInstallmentDiscount = Number(parseFloat($(this).data('discountperinstallment')).toFixed(2));
				discountPerInstallmentTotal += thisSubInstallmentDiscount;
			});

			amtDueToday = Number(parseFloat(Number(amtDueToday) - Number(discountPerInstallmentTotal)).toFixed(2));

			$("##subSummaryTable ##amtDueTodayDisplay").html(getDisplayPrice(amtDueToday));

		}
		function updateSubCardSummaryTotals() {
			let strTotal = getSubRateTotals(true);
			$("##subSummaryTable ##totalDueDisplay").html(getDisplayPrice(strTotal.discountAppliedGrandTotal));
			$("##subSummaryTable .actualSubTotal").html(getDisplayPrice(strTotal.grandTotal));
			refreshAmountDueTodayFromSchedule();
		}
		function roundNumber(num, dec) {
			return Math.round(num*100)/100;
		}
		function getPaymentScheduleFormErrors() {
			var arrReq = [];
			mca_hideAlert('err_schedform');
			if (firstPaymentMinimum > 0 && !checkMinimumUpfront()) {
				arrReq.push('The first payment is required to be at least $'+ firstPaymentMinimum +'.');
			}

			getActiveSchedRowsSelectorArray().each(function () {
				var rowid = $(this).data('rowid');
				var currAmt = $('##ps_' + rowid + '_amt').val() || '';
				var currDate = $('##ps_' + rowid + '_date').val() || '';
				if ((currAmt.length > 0) && (!(currDate.length > 0))) {
					arrReq.push('All amounts must have dates.');
				} else if (currAmt.length > 0) {
					if(!validateSchedAmount(currAmt)){
						arrReq.push('All amounts should be valid dollar amounts.');
					}
					else {
						var stToday = new Date();
						stToday.setHours(0); stToday.setMinutes(0);
						stToday.setSeconds(0); stToday.setMilliseconds(0);
						var stDate = Date.parse(currDate);
						if (stDate < stToday) {
							arrReq.push('All dates must be set to today or later.');
						}
					}
				}
			});

			if (arrReq.length == 0 && !checkSpread()) {
				arrReq.push('Amount Due must equal Amount Scheduled.');
			}

			return arrReq;
		}
		function getAdditionalBillingInfoErrors() {
			var arrReq = [];
			let stateIDforTax = $('##stateIDforTax').val();
			let zipForTax = $('##zipForTax').val();
			if (stateIDforTax == '') arrReq.push('Billing State/Province is required.');
			if (zipForTax == '') arrReq.push('Billing Postal Code is required.');
			if (stateIDforTax > 0 && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDforTax,''))
				arrReq.push('Invalid Billing Postal Code.');

			return arrReq;
		}
		function getActiveSchedRowsSelectorArray(){
			return $('##paymentScheduleTable tbody##tblPaySchRows > tr.payScheduleRow:not(.deletedRow)');
		}
		function setPayScheduleRowIDs(){
			const rowIDsList = $('##paymentScheduleTable tbody##tblPaySchRows > tr.payScheduleRow:not(.deletedRow)')
				.map(function () {
					return $(this).data('rowid');
				}).get().join(',');
			$('##payScheduleRowIDsList').val(rowIDsList);
		}
		function initPaymentScheduleForm(){
			if(invProfilesCount > 1){
				$('##multipleInvProfilesAlert').removeClass('d-none');
			}
			$('##paymentScheduleTable tbody##tblPaySchRows').html('');
			for (let i = 1; i <= numPaymentsToUse; i++) {
				addMoreDates(i, arrPaySchedule[i-1].dateformatted, arrPaySchedule[i-1].amount, false);
			}
			spreadOver(true);

			let discountPerInstallmentTotal = 0;
			$('##frmManageSubs .subCouponRateFields').each(function() {
				let thisSubInstallmentDiscount = Number(parseFloat($(this).data('discountperinstallment')).toFixed(2));
				discountPerInstallmentTotal += thisSubInstallmentDiscount;
			});

			let amtDueToday = Number(parseFloat(Number(arrPaySchedule[0].amount) - Number(discountPerInstallmentTotal)).toFixed(2));

			$("##subSummaryTable ##amtDueTodayDisplay").html(getDisplayPrice(amtDueToday));
		}
		function addMoreDates(rowNum, date, amt, doSpread) {
			if(checkMaxScheduleLimitReached()){
				return;
			}

			var nextRow;
			if(rowNum){
				nextRow = rowNum;
			}
			else {
				var numRows = $('##paymentScheduleTable tbody##tblPaySchRows > tr.payScheduleRow').length;
				nextRow = numRows+1;
			}

			var schedRowTemplate = Handlebars.compile($('##mc_subPaymentScheduleRowHTML').html());
			$('##paymentScheduleTable tbody##tblPaySchRows').append(schedRowTemplate({ rowid: nextRow, date:date || '', amt:amt || '0.00' }));
			refreshSchedRowsNumDisplay();
			checkMaxScheduleLimitReached();

			mca_setupDatePickerField('ps_'+nextRow+'_date');
			mca_setupCalendarIcons('frmManageSubs');

			if(doSpread){
				spreadOver(false);
			}
		}
		function quickLinkSpread(n){
			if(!n || n <= 0) return;
			$('##paymentScheduleTable tbody##tblPaySchRows').html('');
			for (let i = 1; i <= n; i++) {
				addMoreDates(null,null,null,true);
			}
		}
		function removePayScheduleDateRow(i) {
			$('input##ps_'+i+'_date').removeClass('paymentScheduleDateInput').datetimepicker('destroy').val('');
			$('input##ps_'+i+'_amt').removeClass('payScheduleAmt').val('0.00').closest("tr").addClass('d-none');
			var objRow = $('input##ps_'+i+'_amt').closest("tr");
			objRow.addClass('deletedRow');
			var thisRowID = objRow.data('rowid');
			$('tr.payScheduleInvProfRow'+thisRowID).addClass('d-none');
			
			refreshSchedRowsNumDisplay();
			checkMaxScheduleLimitReached();
			spreadOver(false);
		}
		function refreshSchedRowsNumDisplay(){
			var dispNum = 1;
			getActiveSchedRowsSelectorArray().each(function () {
				$(this).find('td.schedRowNumDisplay').html(dispNum + '.');
				dispNum++;
			});
		}
		function checkMaxScheduleLimitReached(){
			var currSchedCount = getActiveSchedRowsSelectorArray().length;
			var limitReached = currSchedCount >= mcsub_maxFreqInstallments;
			$('a##addPaymentBtnLink').toggleClass('disabled',limitReached);
			return limitReached;
		}
		function clearSpread() {
			getActiveSchedRowsSelectorArray().each(function () {
				var rowid = $(this).data('rowid');
				$('##ps_' + rowid + '_date').val('');
				$('##ps_' + rowid + '_amt').val('');
			});

			clearPayOrder();
		}
		function clearPayOrder() {
			getActiveSchedRowsSelectorArray().each(function () {
				var rowid = $(this).data('rowid');
				for (var x=0; x < sPayOrder.length; x++) {
					for (var y=0; y < sPayOrder[x]["profiles"].length; y++) {
						$('##span_' + rowid + '_' + sPayOrder[x]["profiles"][y]["id"] + '_amt').html('');
						$('##hps_' + rowid + '_' + sPayOrder[x]["profiles"][y]["id"] + '_amt').val('');
					}
				}
			});

			for (var x=0; x < sPayOrder.length; x++) {
				sPayOrder[x]["totalApplied"] = 0.00;
				for (var y=0; y < sPayOrder[x]["profiles"].length; y++) {
					sPayOrder[x]["profiles"][y]["totalApplied"] = 0.00;
				}
			}
		}
		function checkSpread() {
			clearPayOrder();
			var totalScheduledAmt = getTotalScheduledAmount();
			var totalsMismatch = (formatCurrency(totalScheduledAmt)!=formatCurrency(amountToCharge));
			$('##amtScheduled').html('$' + formatCurrency(totalScheduledAmt));
			$('##amtScheduled').toggleClass('text-danger', totalsMismatch);
			
			if (totalsMismatch) {
				return false;
			}
			else {
				spreadPayOrder();
				return true;
			}
		}
		function getTotalScheduledAmount(){
			var sum = parseFloat(0.00);

			getActiveSchedRowsSelectorArray().each(function () {
				var rowid = $(this).data('rowid');
				var amt = $('##ps_' + rowid + '_amt').val();
				sum += parseFloat(0 + amt);
			});

			return sum;
		}
		function spreadOver(keepInitalValues) {
			if (!keepInitalValues) {
				clearSpread();
				if(arrPaySchedule.length)
					$('##ps_1_date').val(arrPaySchedule[0].dateformatted);
			}

			var x = getActiveSchedRowsSelectorArray().length;
			var p = amountToCharge;

			var minFirst = firstPaymentMinimum;
			var mp = roundNumber((p-minFirst)/x,2);
			var diff = roundNumber((p-minFirst)-(mp*x),2);
			
			var stDate = Date.parse($('##ps_1_date').val() || new Date());
			<!--- add diff to first month --->
			<!--- ps will be a sparse array - will skip removed row nums indexes --->
			var ps = new Array(x);
			ps[1] = new Object();
			ps[1].amt = roundNumber(mp+diff+minFirst,2);
			ps[1].date = stDate.toString('M/d/yyyy');

			getActiveSchedRowsSelectorArray().each(function () {
				var rowid = $(this).data('rowid');
				if(rowid === 1) return;

				ps[rowid] = new Object();
				ps[rowid].amt = mp;
				if (keepInitalValues) {
					var currDateElem = document.getElementById('ps_' + rowid + '_date');
					if (currDateElem.value.length) {
						stTempDate = Date.parse(currDateElem.value);
						ps[rowid].date = stTempDate.toString('M/d/yyyy');
					}
				}
				else {
					ps[rowid].date = stDate.add(1).months().toString('M/d/yyyy');
				}
			});

			/* using for loop instead of jquery each, as ps will be a sparse array in which inactive row indexes are skipped */
			for (let index in ps) {
				$('##ps_' + index + '_amt').val(ps[index].amt.toFixed(2));
				$('##ps_' + index + '_date').val(ps[index].date);
			}

			checkSpread();
		}
		function spreadPayOrder() {
			var payOrderIndex = 0;
			var isPayOrderIndexInitialized = false;
			var invoiceAmtApplied = parseFloat(0);

			getActiveSchedRowsSelectorArray().each(function () {
				var rowid = $(this).data('rowid');
				var amt = document.getElementById('ps_' + rowid + '_amt').value;
				var arrProfile = {};

				isPayOrderIndexInitialized = false;

				if (amt != '') {
					invoiceAmtApplied = 0;
					amt = parseFloat(amt);
					
					while (roundNumber((amt-invoiceAmtApplied),2) > 0.0) {
						if (!isPayOrderIndexInitialized) {
							for (var x=0; x < sPayOrder[payOrderIndex]["profiles"].length; x++) {
								document.getElementById('hps_' + rowid + '_' + sPayOrder[payOrderIndex]["profiles"][x]["id"] + '_appearOnThisInvoice').value = "true";
							}
							isPayOrderIndexInitialized = true;
						}

						var currAmount = roundNumber((sPayOrder[payOrderIndex]["total"] - sPayOrder[payOrderIndex]["totalApplied"]),2);

						if (roundNumber((amt-invoiceAmtApplied),2) >= currAmount) {
							invoiceAmtApplied += currAmount;
							sPayOrder[payOrderIndex]["totalApplied"] = sPayOrder[payOrderIndex]["total"];
							
							for (var x=0; x < sPayOrder[payOrderIndex]["profiles"].length; x++) {
								var innerCurrAmount = roundNumber((sPayOrder[payOrderIndex]["profiles"][x]["total"] - sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"]), 2);
								sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"] = sPayOrder[payOrderIndex]["profiles"][x]["total"];
								if (!arrProfile[sPayOrder[payOrderIndex]["profiles"][x]["id"]]){
									arrProfile[sPayOrder[payOrderIndex]["profiles"][x]["id"]] = 0;
								} 
								arrProfile[sPayOrder[payOrderIndex]["profiles"][x]["id"]] += innerCurrAmount;
							}
						} else if (roundNumber((amt-invoiceAmtApplied),2) < currAmount) {
							var pctToUse = (amt-invoiceAmtApplied) / currAmount;
							var leftOver = roundNumber((amt-invoiceAmtApplied) - roundNumber((((amt-invoiceAmtApplied) / currAmount) * currAmount),2),2);
				
							for (var x=0; x < sPayOrder[payOrderIndex]["profiles"].length; x++) {
								var innerCurrAmount = roundNumber((sPayOrder[payOrderIndex]["profiles"][x]["total"] - sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"]) * pctToUse, 2);
								sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"] += innerCurrAmount;
								
								if ((leftOver > 0) && (sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"] < sPayOrder[payOrderIndex]["profiles"][x]["total"])) {
									if (leftOver > roundNumber((sPayOrder[payOrderIndex]["profiles"][x]["total"] - sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"]),2)) {
										innerCurrAmount += roundNumber(leftOver - roundNumber((sPayOrder[payOrderIndex]["profiles"][x]["total"] - sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"]),2),2);
										leftOver -= roundNumber((sPayOrder[payOrderIndex]["profiles"][x]["total"] - sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"]),2);
									} else {
										innerCurrAmount += leftOver;
										leftOver = 0;
									}
									sPayOrder[payOrderIndex]["profiles"][x]["totalApplied"] += innerCurrAmount;
								}
								
								if (!arrProfile[sPayOrder[payOrderIndex]["profiles"][x]["id"]]) {
									arrProfile[sPayOrder[payOrderIndex]["profiles"][x]["id"]] = 0;
								} 
								arrProfile[sPayOrder[payOrderIndex]["profiles"][x]["id"]] += innerCurrAmount;
							}
							
							sPayOrder[payOrderIndex]["totalApplied"] += roundNumber((amt-invoiceAmtApplied),2);
							invoiceAmtApplied = amt;
						}
						
						if (roundNumber((sPayOrder[payOrderIndex]["total"]-sPayOrder[payOrderIndex]["totalApplied"]),2) <= 0.0) {
							payOrderIndex++;
							isPayOrderIndexInitialized = false;
						}
					}
				}
				
				var hKeys = Object.keys(arrProfile);
				for(var k=0; k < hKeys.length; k++) {
					setSpanDollarAmt(rowid, hKeys[k], arrProfile[hKeys[k]]);
				} 

			});
		}				
		function setSpanDollarAmt(rowid, pID, amt) {
			document.getElementById('span_' + rowid + '_' + pID + '_amt').innerHTML = amt.toFixed(2);
			document.getElementById('hps_' + rowid + '_' + pID + '_amt').value = amt.toFixed(2);
		}
		function checkMinimumUpfront() {
			var firstAmt = parseFloat(0 + $('##ps_1_amt').val());
			return firstAmt >= firstPaymentMinimum;
		}
		function editStepOne() {
			$('##manageSubsContainer,##subRegCartStepOneTotal').removeClass('d-none');
			$('##subsSummaryContainer').addClass('d-none');
			$('##formStep').val(1);
			top.$('##btnMCModalSave').prop('disabled',false).removeClass('d-none');
			prepTotalsAndNavigation(true);
		}
		function continueToNextStep(){
			if(!addonsLoaded){
				alert('Please wait while the data is still loading.');
				return false;
			}
			if (hasErrorsInSub()) {
				disableContinueBtn();
				return false;
			}

			var step = $('##formStep').val();
			if(step == 1){
				$('##manageSubsContainer,##subRegCartStepOneTotal').addClass('d-none');
				$('##subsSummaryContainer').removeClass('d-none');
				$('##formStep').val(2);
				top.$('##btnMCModalSave').prop('disabled',true).addClass('d-none');

				/* populate summary data */
				let rootSubRatObj = $('##rootSubWrapper input.subRateRadio:checked');
				let rootSubRate = Number(parseFloat(rootSubRatObj.data('price')));
				let rootSubTermRate = Number(parseFloat(rootSubRatObj.data('termprice')));
				let rootFreqName = rootSubRatObj.data('freqname');
				var showFreqColumn = rootFreqName != 'Full';
				$('span##termDatesDisp').text('(' + $('##fTermFrom').val() + ' - ' + $('##fTermTo').val() + ')');

				let subRateRowTemplate = Handlebars.compile($('##mc_subRateRowHTML').html());
				let objRootRate = {
					idkey: 'rootSub',
					subid: rootSubRatObj.attr('rate-subscriptionid'),
					rfid: rootSubRatObj.val(),
					isfreeaddonsub:false,
					name: rootSubRatObj.data('ratename'),
					ratedisplay: getDisplayPrice(rootSubRate,false),
					termrateoriginal:rootSubTermRate.toFixed(2),
					termratetouse:rootSubTermRate.toFixed(2),
					freqname: rootSubRate > 0 ? rootFreqName : '',
					showfreqname:showFreqColumn
				};

				let editableRootSubRateField = $('##newRateTotal_'+objRootRate.subid+'_'+objRootRate.rfid);
				let editableRootSubRate = editableRootSubRateField.length == 1;
				if (editableRootSubRate) {
					let rootSubRateAmt = editableRootSubRateField.val().replace(',','');
					let totalRootSubRateAmt = Number(rootSubRateAmt) * Number(editableRootSubRateField.data('rateinstallments'));
					
					objRootRate.termratetouse = Number(totalRootSubRateAmt.toFixed(2));
					objRootRate.ratedisplay = getDisplayPrice(rootSubRateAmt,false);
				}

				$('##subSummaryTable div##rootSubRateSelection').html(subRateRowTemplate(objRootRate));
				
				var fnGetAddonsArray = function(objSelector,isInline){
					var arraddons = []
					objSelector.each(function() {
						var thisAddonCard = $(this);
						var arrCheckedSubsSelector = isInline ? thisAddonCard.find('input.subSelector:checked') : thisAddonCard.find('input.subSelector:checked').not('.inlineAddOn input.subSelector');
						if(arrCheckedSubsSelector.length > 0){
							var strAddons = { setname: '', arrsubs: [] };
							if(!isInline){
								let linkedFormContainer = thisAddonCard.data('linkedformcontainer');
								let childSetID = $('##' + linkedFormContainer).data('childsetid');
								strAddons.setname = $('##subSet'+childSetID).data('setname');
							}
							else {
								strAddons.setname = thisAddonCard.data('setname');
							}
							arrCheckedSubsSelector.each(function() {
								let addonSubID = $(this).val();
								var isFreeAddonOnSub =  $(this).data('isfreeaddonsub') || 0;
								let subName = $(this).data('subscriptionname');
								let thisID = $(this).attr('id');
								let selectedRateFieldObj = $('input.subRateRadio[name="sub'+addonSubID+'_rfid"][data-linkedsubinputid="'+thisID+'"]:checked');
								let rfid = selectedRateFieldObj.val();

								let editableRateField = $('##newRateTotal_'+addonSubID+'_'+rfid);
								let editableRate = editableRateField.length == 1;

								let subRate;
								let subTermRateOriginal = Number(parseFloat(selectedRateFieldObj.data('termprice')));
								let subTermRateToUse;
								let freqName;
								if(isFreeAddonOnSub){
									subRate = 0;
									subTermRateToUse = 0;
									freqName = '';
								}
								else {
									subRate = Number(parseFloat(selectedRateFieldObj.data('price')));
									subTermRateToUse = subTermRateOriginal;
									freqName = selectedRateFieldObj.data('freqname');
									freqName = freqName != 'Full' && subRate > 0 ? ' ' + freqName : '';

									if (editableRate) {
										subRate = editableRateField.val().replace(',','');
										let totalRateAmt = Number(subRate) * Number(editableRateField.data('rateinstallments'));
										
										subTermRateToUse = Number(totalRateAmt.toFixed(2));
									}
								}

								let childaddons = [];
								let arrInlineAddonsSelector = thisAddonCard.find('div##sub'+addonSubID+'_addons .inlineAddOn');
								if(arrInlineAddonsSelector.length){
									childaddons = fnGetAddonsArray(arrInlineAddonsSelector,true);
								}
								strAddons.arrsubs.push({
									idkey:addonSubID+'_'+rfid,
									subid: addonSubID,
									rfid:rfid,
									isfreeaddonsub:isFreeAddonOnSub == 1,
									name: subName,
									ratedisplay: getDisplayPrice(subRate,false),
									termrateoriginal:subTermRateOriginal.toFixed(2),
									termratetouse:subTermRateToUse.toFixed(2),
									freqname: freqName,
									showfreqname:showFreqColumn,
									childaddons:childaddons
								});								
							});
							arraddons.push(strAddons);
						}
					});

					return arraddons;
				}
				var objRatesSummary = { arraddons: fnGetAddonsArray($('.addOnCards'),false) };
				var subSelectionSummaryTemplate = Handlebars.compile($('##mc_subSelectionSummaryHTML').html());
				if(objRatesSummary.arraddons.length){
					var subSelectionSummaryTemplate = Handlebars.compile($('##mc_subSelectionSummaryHTML').html());
					$('##subSummaryTable div##addOnSubsSelection').html(subSelectionSummaryTemplate(objRatesSummary));
				}

				var strTotal = getSubRateTotals(false);
				$("##subSummaryTable ##totalDueDisplay").html(getDisplayPrice(strTotal.discountAppliedGrandTotal));
				$("##subSummaryTable ##amtDueTodayDisplay").html(getDisplayPrice(strTotal.grandTotal));

				if(showFreqColumn) {
					setTimeout(() => {
						let freqWidth = 0;
						$('##subSummaryTable .freqDisplayElm').each(function() {
							if ($(this).text().trim() !== '') {
								freqWidth = $(this).outerWidth();
								return false;
							}
						});
						$('##subSummaryTable .freqDisplayElm').css('width',freqWidth+'px');
						$('##subSummaryTable .freqDisplayElm').removeClass('d-none');
					}, 100);
				} else {
					$('##subSummaryTable .freqDisplayElm').addClass('d-none');
				}
				initSummaryStep();
				$("html, body").animate({scrollTop: 0}, 750);
			}
		}
		function refreshTermDatesCard(){
			$('div##rootSubTermDates').removeClass('d-none');
			var formObj = $('##rootSubTermDates ##subTermDatesSummary');
			formObj.find('##termStart').text(strTermDates.rootSub.subStartDate);
			formObj.find('##termEnd').text(strTermDates.rootSub.subEndDate);
			$('##fTermFrom').val(strTermDates.rootSub.subStartDate);
			$('##fTermTo').val(strTermDates.rootSub.subEndDate);
			$('##fTermGrace').val(strTermDates.rootSub.graceEndDate);
			$('##termDatesEditBtn').html('<span><i class="fa-solid fa-pencil mr-1"></i> Edit</span>').removeClass('d-none');

			<cfif local.useAccrualAcct>
				let inputkey = 'addon0' + '_sub' + mcsub_rootsubid + '_rfid' + strTermDates.rootSub.rfid;
				addRecogDatesRow(inputkey);
			<cfelse>
				reInitTermDateFields();
			</cfif>
		}
		<cfif local.useAccrualAcct>
			function getRecognitionDates(subStartDate, subEndDate, recogStartDate, recogEndDate) {
				var retStruct = {};
				var rootSubTermFlag = '#local.strRootSub.subscription[1].rateTermDateFlag#';

				// Local helper functions
				var parseDate = function(str) {
					return new Date(str);
				};

				var formatDate = function(date) {
					const d = new Date(date);
					return (d.getMonth() + 1) + '/' + d.getDate() + '/' + d.getFullYear();
				};

				// If recogStartDate or recogEndDate is blank, use sub dates
				let hasValidRecogStart = recogStartDate && recogStartDate.trim().length > 0;
				let hasValidRecogEnd = recogEndDate && recogEndDate.trim().length > 0;

				let recogStartDateObj = hasValidRecogStart ? parseDate(recogStartDate) : parseDate(subStartDate);
				let recogEndDateObj = hasValidRecogEnd ? parseDate(recogEndDate) : parseDate(subEndDate);

				// recogStartDate can't be before saleTransactionDate or subStartDate
				let saleDate = parseDate('#DateFormat(now(), "m/d/yyyy")#');
				let subStart = parseDate(subStartDate);
				let subEnd = parseDate(subEndDate);

				if (saleDate > recogStartDateObj) {
					recogStartDateObj = new Date(saleDate);
				}
				if (subStart > recogStartDateObj) {
					recogStartDateObj = new Date(subStart);
				}

				// Term flag 'C' — spread recognition over original number of days
				if (rootSubTermFlag === "C" && hasValidRecogStart && hasValidRecogEnd) {
					let diffDays = Math.round((parseDate(recogEndDate) - parseDate(recogStartDate)) / (1000 * 60 * 60 * 24));
					recogEndDateObj = new Date(recogStartDateObj);
					recogEndDateObj.setDate(recogEndDateObj.getDate() + diffDays);
				}

				// recogEndDate cannot be after subEndDate
				if (recogEndDateObj > subEnd) {
					recogEndDateObj = new Date(subEnd);
				}

				// recogStartDate must be before or equal to recogEndDate
				if (recogStartDateObj > recogEndDateObj) {
					recogEndDateObj = new Date(recogStartDateObj);
				}

				retStruct.recogStartDate = formatDate(recogStartDateObj);
				retStruct.recogEndDate = formatDate(recogEndDateObj);

				return retStruct;
			}
			function addRecogDatesRow(inputkey){
				var addonID = inputkey.split('_')[0].replace('addon', '');
				var subID = inputkey.split('_')[1].replace('sub', '');
				var rfID = inputkey.split('_')[2].replace('rfid', '');
				var subInput = addonID > 0 ? $('##subAddOn'+addonID+'_'+subID) : $('##sub'+subID);

				var recogStartDate = '';
				var recogEndDate = '';

				let subStartDate = $('##fTermFrom').val();
				let subEndDate = $('##fTermTo').val();

				if(addonID > 0){
					let linkedSubInputID = 'subAddOn' + addonID + '_' + subID;
					let rateRadioName = 'sub' + subID + '_rfid';
					let selectedRateFieldObj = $('input.subRateRadio[name="'+rateRadioName+'"][data-linkedsubinputid="'+linkedSubInputID+'"]:checked');
					let initRecogStartDate = selectedRateFieldObj.data('recogstartdate');
					let initRecogEndDate = selectedRateFieldObj.data('recogenddate');

					/* get recognition dates after adjustments */
					let strRecogDates = getRecognitionDates(subStartDate, subEndDate, initRecogStartDate, initRecogEndDate);
					recogStartDate = strRecogDates.recogStartDate;
					recogEndDate = strRecogDates.recogEndDate;
				}
				else {
					/* get recognition dates after adjustments */
					let strRecogDates = getRecognitionDates(subStartDate, subEndDate, strTermDates.rootSub.recogStartDate, strTermDates.rootSub.recogEndDate);
					recogStartDate = strRecogDates.recogStartDate;
					recogEndDate = strRecogDates.recogEndDate;
				}

				var templObj = {
					inputkey: inputkey,
					subname: subInput.data('subscriptionname'),
					frecogfromdate: recogStartDate,
					frecogtodate: recogEndDate
				};
				var recogDateRowTemplate = Handlebars.compile($('##mc_recogDateRowHTML').html());

				if($('table##tblRecogDates tbody tr.recogRow_' + templObj.inputkey).length == 0){
					$('table##tblRecogDates tbody').append(recogDateRowTemplate(templObj));

					mca_setupDatePickerField('fRecogFrom_' + templObj.inputkey, $('##fTermFrom').val(), $('##fTermTo').val());
					mca_setupDatePickerField('fRecogTo_' + templObj.inputkey, $('##fRecogFrom_' + templObj.inputkey).val(), $('##fTermTo').val());

					$('##fRecogFrom_' + templObj.inputkey).off('change').change(function(){ rebindRecogToField(templObj.inputkey) });

					rebindRecogFromField(templObj.inputkey);
					rebindRecogToField(templObj.inputkey);
				}

				reInitTermDateFields();
			}
			function rebindRecogFromField(inputkey){
				bindToToFrom($('##fTermFrom'),$('##fRecogFrom_' + inputkey),$('##fTermTo'));
			}
			function rebindRecogToField(inputkey){
				bindToToFrom($('##fRecogFrom_' + inputkey),$('##fRecogTo_' + inputkey),$('##fTermTo'));
			}
		</cfif>
		function reInitTermDateFields(){
			<cfif local.useAccrualAcct>
				var arrRecogDateKeys = [];
				$('tr.recogDateRow').each(function() { arrRecogDateKeys.push($(this).data('inputkey')); });
			</cfif>

			mca_setupDatePickerField('fTermFrom');
			$('##fTermFrom').off('change').change( function(e) {
				<cfif local.useAccrualAcct>
					$.each(arrRecogDateKeys, function(index, thisInputKey) {
						rebindRecogFromField(thisInputKey);
					});
				</cfif>
				bindToToFrom($('##fTermFrom'),$('##fTermTo'));
			});

			mca_setupDatePickerField('fTermTo',$('##fTermFrom').val());
			$('##fTermTo').off('change').change( function(e) {
				<cfif local.useAccrualAcct>
					$.each(arrRecogDateKeys, function(index, thisInputKey) {
						rebindRecogToField(thisInputKey);
						rebindRecogFromField(thisInputKey);
					});
				</cfif>
				bindToToFrom($('##fTermTo'),$('##fTermGrace'));
			});

			mca_setupDatePickerField('fTermGrace',$('##fTermTo').val());

			mca_setupCalendarIcons('frmManageSubs');
		}
		function bindToToFrom(s,e,l) {
			l = (typeof l === 'undefined') ? '' : l;
			e.datetimepicker('destroy');
			if (e.val().length > 0) { 
				if (mca_getParsedDateTime(s.val()) > mca_getParsedDateTime(e.val())) e.val(s.val()); 
				else if (l.length > 0 && mca_getParsedDateTime(l.val()) < mca_getParsedDateTime(e.val())) e.val(l.val());
			}
			if (l.length > 0) {
				mca_setupDatePickerField(e.attr('id'), s.val(), l.val());
			} else {
				mca_setupDatePickerField(e.attr('id'), s.val());
			}
			e.trigger('change');
		}
		function editTermDates() {
			subTermDatesCache = {};
			$('##subTermDatesForm input.dateControl').each(function() {
				subTermDatesCache[$(this).attr('id')] = $(this).val();
			});

			toggleTermDatesFormDisplay(true);
		}
		function confirmTermDatesChanges() {
			var fTermFrom = $('##fTermFrom').val() || '';
			var fTermTo = $('##fTermTo').val() || '';

			if(fTermFrom.length && fTermTo.length){
				var formObj = $('##rootSubTermDates ##subTermDatesSummary');
				formObj.find('##termStart').text($('##fTermFrom').val());
				formObj.find('##termEnd').text($('##fTermTo').val());
			}
			else {
				if(subTermDatesCache.hasOwnProperty('fTermFrom')) $('##fTermFrom').val(subTermDatesCache['fTermFrom']);
				if(subTermDatesCache.hasOwnProperty('fTermTo')) $('##fTermTo').val(subTermDatesCache['fTermTo']);
				if(subTermDatesCache.hasOwnProperty('fTermGrace')) $('##fTermGrace').val(subTermDatesCache['fTermGrace']);
			}

			toggleTermDatesFormDisplay(false);
			return true;
		}
		function cancelTermDatesForm() {
			$('##subTermDatesForm input.dateControl').each(function() {
				let thisFldID = $(this).attr('id');
				if(subTermDatesCache.hasOwnProperty(thisFldID)) $(this).val(subTermDatesCache[thisFldID]);
			});
			$('##subTermDatesForm input.dateControl').trigger('change');
			toggleTermDatesFormDisplay(false);
		}
		function toggleTermDatesFormDisplay(f){
			$('##subTermDatesForm').toggleClass('d-none',!f);
			$('##subTermDatesSummary').toggleClass('d-none',f);
			if(f) toggleCardsOverlay(true,'rootSubTermDates');
			else toggleCardsOverlay(false);
		}
		<cfif local.useAccrualAcct>
			function refreshRecognitionDatesForSub(subID, isChecked){
				var arrCurrentRecogDateKeys = [];
				$('tr.recogDateRow').each(function() { arrCurrentRecogDateKeys.push($(this).data('inputkey')); });

				var arrNewRecogDateKeys = [];
				$('input.subSelector:checked').each(function() {
					let thisSubID = $(this).val();
					let thisAddOnID = $(this).data('addonid');
					let selectedRate = $('input[name="sub'+thisSubID+'_rfid"]:checked');
					if(selectedRate){
						var thisRFID = selectedRate.val();
						var thisInputKey = 'addon' + thisAddOnID + '_sub' + thisSubID + '_rfid' + thisRFID;
						arrNewRecogDateKeys.push(thisInputKey);
					}
				});

				/* remove unselected recog date rows */
				$.each(arrCurrentRecogDateKeys, (_, val) => {
					if (!arrNewRecogDateKeys.includes(val)) {
						$('tr##recogDateRow_' + val).remove();
						reInitTermDateFields();
					}
				});

				/* add new recog date rows */
				$.each(arrNewRecogDateKeys, (_, val) => {
					if (!arrCurrentRecogDateKeys.includes(val)) {
						addRecogDatesRow(val)
					}
				});
			}
		</cfif>
		/* add-ons */
		function loadSubAddOns(fd,onLoadAddonsComplete) {
			addonsLoaded = false;
			$('##sub'+mcsub_rootsubid+'_addons').html('');
			$('##sub'+mcsub_rootsubid+'_addons')
				.html($('##subLoadingCard').html())
				.load('#local.loadSubAddOnsLink#', fd, onLoadAddonsComplete);
		}
		function validateOnLoadSubAddOns() {
			if (!$('.subAddOnFormContainer').length) return false;
			
			$('.subAddOnFormContainer').each(function() {
				let addOnID = $(this).data('addonid');
				let maxAllowed = $(this).data('maxallowed');
				let inputName = 'subAddOn'+addOnID;

				/* disable unchecked checkboxes */
				if (maxAllowed > 1 && $('input.subSelector[name="'+inputName+'"]:checked').length >= maxAllowed) {
					$('input.subSelector[name="'+inputName+'"]').not(':checked').prop('disabled',true);
					$('input.subSelector[name="'+inputName+'"]:disabled').parent().find('label').addClass('text-dim');
				}
			});

			/* min/max checks */
			$('.addOnCards').each(function() {
				validateAddOnSubMinMaxSelections($(this).find('.subAddOnFormContainer'));
				let addOnFooterContainer = $(this).find('.addOnFooterContainer');
				if ($(this).find('.alert').length) {
					if ($(this).find('.addOnCardBody').attr('data-errcode') == 'setmax') {
						$(this).find('.inlineAddOn').addClass('opacity-4');
						$(this).find('.inlineAddOn .subSelector').prop('disabled',true);
					}
				}
			});

			/* auto expand add-ons having errors */
			$('.addOnCards').each(function() {
				if ($(this).attr('data-errfields').length) {
					$(this).find('.card-header').removeClass('bg-light').addClass('bg-light-danger');
				}
			});
		}
		function validateAddOnSubSelections(parentSubID,addOnID,subID,isChecked) {
			let subAddOnForm =  $('##sub'+parentSubID+'_addonID'+addOnID+'_formWrapper');
			let addOnTitleContainer = $('.sub'+parentSubID+'_addonID'+addOnID+'_titleContainer');
			let subAddOnCard = subAddOnForm.closest('.addOnCards');
			let minAllowed = subAddOnForm.data('minallowed');
			let maxAllowed = subAddOnForm.data('maxallowed');
			let freeSubsCount = subAddOnForm.data('itemsfree');
			let inputName = 'subAddOn'+addOnID;
			let numCheckedSubs = $('input.subSelector[name="'+inputName+'"]:checked').length;
			
			if (maxAllowed == 1) {
				let checkedSubID = $('input.subSelector[name="'+inputName+'"]:checked').val();

				$('input.subSelector[name="'+inputName+'"][value!="'+checkedSubID+'"]').each(function() {
					let thisSubID = $(this).val();
					$("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").prop('checked', false);
					$('.sub'+thisSubID+'_addons input.subSelector:checked, .sub'+thisSubID+'_addons input.subRateRadio:checked').prop('checked', false);
					$('##sub'+thisSubID+'_addons').addClass('d-none');
				});
			} else if (maxAllowed > 1) {
				if ($('input.subSelector[name="'+inputName+'"]:checked').length >= maxAllowed) {
					$('input.subSelector[name="'+inputName+'"]').not(':checked').prop('disabled',true);
					$('input.subSelector[name="'+inputName+'"]:disabled').parent().find('label').addClass('text-dim');
				} else {
					$('input.subSelector[name="'+inputName+'"][data-issubscribed="0"]').not(':checked').prop('disabled',false);
					$('input.subSelector[name="'+inputName+'"]:not(:disabled)').parent().find('label').removeClass('text-dim');
				}
			}

			/* validation errors */
			let hasErr = false;
			if (isChecked && numCheckedSubs > 0) {
				if (minAllowed > 0 && numCheckedSubs < minAllowed) {
					hasErr = true;
				} else if (maxAllowed > 0 && numCheckedSubs > maxAllowed) {
					hasErr = true;
				}
			}

			if (!hasErr) {
				let prevErrCode = subAddOnForm.attr('data-errcode');
				if (prevErrCode == 'setmax') {
					if (subAddOnForm.find('.inlineAddOn').length) {
						subAddOnForm.find('.inlineAddOn').removeClass('opacity-4');
						subAddOnForm.find('.inlineAddOn .subSelector[data-issubscribed="0"]').prop('disabled',false);

						subAddOnForm.find('.inlineAddOn').each(function() {
							let thisSubAddOnForm =  $(this).find('.subAddOnFormContainer');
							if (thisSubAddOnForm.attr('data-errcode') == 'setmax') {
								let thisSubAddOnID = thisSubAddOnForm.data('addonid');
								let thisSubAddOnSubInputName = 'subAddOn'+thisSubAddOnID;
								$('input.subSelector[name="'+thisSubAddOnSubInputName+'"]').not(':checked').prop('disabled',true);
								$('input.subSelector[name="'+thisSubAddOnSubInputName+'"]:disabled').parent().find('label').addClass('text-dim');
							}
						});
					}
				}

				/* remove alerts */
				subAddOnForm.attr('data-errcode','');
				hideSubMainCardAlert(subAddOnCard,subAddOnForm.attr('id'));

				subAddOnCard.find('.card-header').addClass('bg-light').removeClass('bg-light-danger');
				addOnTitleContainer.find('.addOnTitleAlert').removeClass('mt-2').html('');
				subAddOnForm.find('.addOnSelMsg').removeClass('d-none');

				$('##sub'+subID+'_addons').find('.subAddOnFormContainer').each(function() {
					$(this).attr('data-errcode','');
					hideSubMainCardAlert(subAddOnCard,$(this).attr('id'));
				});

				let innerAddOnTitleContainer = $('##sub'+subID+'_addons').find('.innerAddOnTitleContainer');
				innerAddOnTitleContainer.removeClass('title-alert alert alert-danger');
				innerAddOnTitleContainer.find('.addOnTitleAlert').removeClass('mt-2').html('');
				
				if (!subAddOnCard.find('.bg-light-danger').length) {
					let addOnFooterContainer = subAddOnCard.find('.addOnFooterContainer');
					addOnFooterContainer.find('.addOnBottomAlert').html('').removeClass('alert alert-danger');
				}
			}

			if (freeSubsCount) {
				let arrSavedFreeSubs = $('##addOn'+addOnID+'_freeSubs').val().length ? $('##addOn'+addOnID+'_freeSubs').val().split(',').map(Number) : [];
				let arrFreeSubs = arrSavedFreeSubs.filter(function(freeSubID) { 
					return $('##subAddOn'+addOnID+'_'+freeSubID).is(':checked');
				});

				$('input.subSelector[name="'+inputName+'"]').not(':checked').each(function() {
					$(this).attr('data-isfreeaddonsub',0);
					$('.sub'+$(this).val()+'RateAmtDisp').removeClass('d-none');
					let index = arrFreeSubs.indexOf($(this).val());
					if (index > -1) {
						arrFreeSubs.splice(index, 1);
					}
				});

				let freeSubsCounter = arrFreeSubs.length;
				$('input.subSelector[name="'+inputName+'"]:checked').each(function() {
					let thisSubID = $(this).val();
					let rfid = $("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").val();

					/* if editable price */
					if ($('##newRateTotal_'+thisSubID+'_'+rfid).length) return;

					if (freeSubsCounter < freeSubsCount && arrFreeSubs.indexOf(Number(thisSubID)) == -1) {
						$(this).attr('data-isfreeaddonsub',1);
						arrFreeSubs.push(Number(thisSubID));
						freeSubsCounter++;
					}

					if ($(this).attr('data-isfreeaddonsub') == 1) {
						$('##sub'+thisSubID+'_rfid_'+rfid+'_rateAmtDisp').addClass('d-none');
					} else {
						$('##sub'+thisSubID+'_rfid_'+rfid+'_rateAmtDisp').removeClass('d-none');
					}
				});

				let freeSubIDList = arrFreeSubs.length ? arrFreeSubs.join(',') : '';
				$('##addOn'+addOnID+'_freeSubs').val(freeSubIDList);
			}
		}
		function validateAddOnSubMinMaxSelections(subAddOnFormContainers) {
			subAddOnFormContainers.each(function() {
				let subID = $(this).data('subscriptionid');
				if (!$('input.subSelector[value="'+subID+'"]').is(':checked')) return;
				
				let errMsg = '';
				let errCode = '';
				let minAllowed = $(this).data('minallowed');
				let maxAllowed = $(this).data('maxallowed');
				let addOnID = $(this).data('addonid');
				let inputName = 'subAddOn'+addOnID;
				let numCheckedSubs = $('input.subSelector[name="'+inputName+'"]:checked').length;
				let addOnTitleContainer = $('.subAddOn'+addOnID+'TitleContainer');
				let isInlineAddOn = $(this).parent().hasClass('inlineAddOn');
				let subMainCard = $(this).closest('.mainSubCard');
			
				if (minAllowed > 0 && numCheckedSubs < minAllowed) {
					errMsg = $(this).find('.addOnSelMsg').html();
					errCode = 'setmin';
				} else if (maxAllowed > 0 && numCheckedSubs > maxAllowed) {
					errMsg = $(this).find('.addOnSelMsg').html() + ' Current selections exceed the maximum of ' + maxAllowed + '.';
					errCode = 'setmax';
				}
				if (errMsg.length) {
					subMainCard.find('.card-header').removeClass('bg-light').addClass('bg-light-danger');
					addOnTitleContainer.find('.addOnTitleAlert').addClass('mt-2').html(errMsg);
					$(this).find('.addOnSelMsg').addClass('d-none');
					$(this).attr('data-errcode',errCode);

					showSubMainCardAlert(subMainCard,$(this).attr('id'),(isInlineAddOn ? '' : errMsg));
				} else {
					subMainCard.find('.card-header').addClass('bg-light').removeClass('bg-light-danger');
					addOnTitleContainer.find('.addOnTitleAlert').removeClass('mt-2').html('');
					$(this).find('.addOnSelMsg').removeClass('d-none');
					$(this).attr('data-errcode','');
					hideSubMainCardAlert(subMainCard,$(this).attr('id'));
				}
			});
		}
		/* sub rates */
		function chooseRate(thisObj) {
			if(thisObj.checked) {
				let freqID = $(thisObj).data("freqid");
				let rfid = $(thisObj).val();
				
				/* assoc sub */
				let subInput = $('##'+$(thisObj).data("linkedsubinputid"));
				let subID = subInput.val();
				
				if (!subInput.is(':checked')) {
					subInput.trigger("click");
				}

				hideSubMainCardAlert(subInput.closest('.mainSubCard'),subInput.attr('id'));

				/* hide all edit rate price fields */
				$('##sub'+subID+'_rates').find('.editRatePrices').addClass('d-none');
				/* show all rate prices */
				$('##sub'+subID+'_rates').find('.dspRatePrices').removeClass('d-none');

				/* can edit rate price */
				if ($('.editRatePrice'+rfid).length) {
					$('.editRatePrice'+rfid).removeClass('d-none');
					$('.dspRatePrice'+rfid).addClass('d-none');
				}

				let editableRateField = $('##newRateTotal_'+subID+'_'+rfid);
				if (editableRateField.length) {
					doValidateRateAmt(editableRateField[0],false);
				}

				/* show summary form after rate selection */
				if (subID == mcsub_rootsubid) {
					if (!editableRateField.length) {
						confirmRootSubChanges();
					}

					strTermDates["rootSub"]["rfid"] = rfid;
					strTermDates["rootSub"]["subStartDate"] = $(thisObj).data("termstartdate");
					strTermDates["rootSub"]["subEndDate"] = $(thisObj).data("termenddate");
					strTermDates["rootSub"]["graceEndDate"] = $(thisObj).data("graceenddate");
					strTermDates["rootSub"]["recogStartDate"] = $(thisObj).data("recogstartdate");
					strTermDates["rootSub"]["recogEndDate"] = $(thisObj).data("recogenddate");
					refreshTermDatesCard();
				}

				if (subID == mcsub_rootsubid && mcsub_rootsubratefreqid != freqID) {
					mcsub_rootsubratefreqid = freqID;
					
					let arrSelectedAddOns = $('##sub'+mcsub_rootsubid+'_addons').find('input.subSelector:checked').map(function() { return $(this).data('addonid'); }).get();
						arrSelectedAddOns = arrSelectedAddOns.filter((v, i, a) => a.indexOf(v) === i);
					let arrSelectedAddOnSubs = $('##sub'+mcsub_rootsubid+'_addons').find('input.subSelector:checked').map(function() { return $(this).val(); }).get();

					let fd = { parentRFID:rfid, freeratedisplay:mcsub_freeratedisplay, userenewalrate:0, 
						selectedAddOns:arrSelectedAddOns.join(','), selectedAddOnSubs:arrSelectedAddOnSubs.join(',') };
					
					arrSelectedAddOns.forEach(function(addOnID) {
						if ($('##addOn'+addOnID+'_freeSubs').length) {
							fd['addOn'+addOnID+'_freeSubs'] = $('##addOn'+addOnID+'_freeSubs').val();
						}
					});

					$('##sub'+mcsub_rootsubid+'_addons').find('input.subSelector:checked').each(function() {
						let thisAddOnID = $(this).data('addonid');
						let thisSubID = $(this).val();
						let selectedRate = $('input[name="sub'+thisSubID+'_rfid"]:checked');

						if (!selectedRate) return;

						fd['sub'+thisSubID+'_currRFID'] = selectedRate.val();
						fd['sub'+thisSubID+'_currFreqID'] = selectedRate.data('freqid');
						fd['sub'+thisSubID+'_currRateID'] = selectedRate.data('rateid');
						
						let editableRootSubRateField = $('##newRateTotal_'+thisSubID+'_'+selectedRate.val());
						if (editableRootSubRateField.length) {
							fd['sub'+thisSubID+'_newRateAmt'] = Number(editableRootSubRateField.val().replace(',',''));
						}
					});

					disableContinueBtn();

					/* reload addons */
					var onLoadAddonsComplete = function(responseText, textStatus, xhr){
						if (textStatus !== 'success') {
							console.log('Addons Load Failed: ' + textStatus);
							alert('An error occured while loading addons info.');
							disableContinueBtn();
							return;
						}
						addonsLoaded = true;
						defineEditHandlerForSubCards('reloadAddOn');
						validateOnLoadSubAddOns();
						prepTotalsAndNavigation(true);
					}
					loadSubAddOns(fd,onLoadAddonsComplete);
				}
				<cfif local.useAccrualAcct>
					refreshRecognitionDatesForSub(subID,true);
				</cfif>
			}

			removeRateAlerts($(thisObj).closest('.subsContainer'));
			prepTotalsAndNavigation(false);
		}
		function defineEditHandlerForSubCards(mode) {
			let subCardScope = mode == 'init' 
								? $('.subCardSummary') 
								: $('##sub'+mcsub_rootsubid+'_addons').find('.subCardSummary');
			
			subCardScope.find('.subCardEditBtn').off('click').on('click',function() {
				let editBtn = $(this);
				if (editBtn.length) {
					let editMode = editBtn.data('editmode');
					if (editMode == 'editrootsub') {
						editRootSub();
					}
					else if (editMode == 'edittermdates') {
						editTermDates();
					} else {
						let addonid = editBtn.data('addonid');
						let subscriptionid = editBtn.data('subscriptionid');
						subAddOnsCache = $('##sub'+mcsub_rootsubid+'_addons').clone();
						editSubAddOn(addonid,subscriptionid);
					}
				}
			});
		}
		function toggleCardsOverlay(f,currentCardID) {
			let subCard = $('##'+currentCardID);
			if(f) {
				subCard.find('.subsContainer:first').addClass('mcsubs-active-card-body');

				let activeSubCard = subCard[0];
				let clientHeight = document.documentElement.clientHeight;
				let activeCardTop = activeSubCard.getBoundingClientRect().y;
				let activeCardBottom = activeSubCard.getBoundingClientRect().bottom;
				let addOnSubFrontEndContentHeight = subCard.find('.addOnSubFrontEndContent:first').height();
				let hdrFtrSubFEContentTotalHeight = 250 + addOnSubFrontEndContentHeight;

				if (activeCardBottom > clientHeight) {
					let calcHeight = parseInt(clientHeight - (activeCardTop + hdrFtrSubFEContentTotalHeight));

					const activateSubCardPromise = new Promise(function(resolve,reject) {
						if (calcHeight < hdrFtrSubFEContentTotalHeight && activeCardTop + calcHeight < activeCardBottom) {						
							let delayInMS = 750;

							$('html, body').animate({
								scrollTop: subCard.offset().top - 175
							}, delayInMS);

							mca_delayFunc(delayInMS + 100)
							.then(function() {
								activeCardTop = activeSubCard.getBoundingClientRect().y;
								calcHeight = parseInt(clientHeight - (activeCardTop + hdrFtrSubFEContentTotalHeight));
								resolve();
							});
							
						} else {
							resolve();
						}
					}).then(function() {
						calcHeight = calcHeight < 300 ? 300 : calcHeight;
						subCard.find('.subsContainer:first').css('max-height',calcHeight+'px');
					});
				} else {
					$('html, body').animate({
						scrollTop: subCard.offset().top - 175
					}, 750);
					calcHeight = parseInt(clientHeight/2) < 300 ? 300 : parseInt(clientHeight/2);
					subCard.find('.subsContainer:first').css('max-height',parseInt(clientHeight/2)+'px');
				}

				$('div.mainSubCard').not('##' + currentCardID).addClass('card-disabled-overlay');
				disableContinueBtn();
			} else {
				subCard.find('.subsContainer:first').removeClass('mcsubs-active-card-body');
				$('div.mainSubCard').removeClass('card-disabled-overlay');
				enableContinueBtn();
			}
		}
		function editRootSub() {
			toggleCardsOverlay(true,'rootSubWrapper');
			$('##rootSubSummary').addClass('d-none');
			$('##rootSubForm').removeClass('d-none');

			/* show cancel button while editing */
			let arrCheckedRadioObj = $('##rootSubWrapper input.subRateRadio:checked');
			if(arrCheckedRadioObj.length || $('##rootSubForm').find('.editableRateFields').length){
				$('##rootSubConfirmBtn').removeClass('d-none');
			}
		}
		function confirmRootSubChanges() {
			let rootSubForm = $('##rootSubForm');
			
			/* reverting to orig rate amts for unchecked rate fields having alerts */
			removeRateAlerts(rootSubForm);

			if (! $('##rootSubForm').find('input.subRateRadio:checked').length) return false;

			/* if editable price */
			if (rootSubForm.find('.editableRateFields').length) {
				let rootSubRFID = $('input[name="sub'+mcsub_rootsubid+'_rfid"]:checked').val();
				let editableRate = $('##newRateTotal_'+mcsub_rootsubid+'_'+rootSubRFID);
				if (editableRate.length) {
					doValidateRateAmt(editableRate[0],false);
				}
			}
			
			/* any alerts */
			let alertContainer = rootSubForm.find('.alert:first');
			if (alertContainer.length) {
				$('html, body').animate({
					scrollTop: alertContainer.offset().top - 175
				}, 750);

				return false;
			}

			$('##rootSubSummary').removeClass('d-none');
			rootSubForm.addClass('d-none');
			prepTotalsAndNavigation(false);
			toggleCardsOverlay(false);
			return true;
		}
		function editSubAddOn(aoID,sid) {
			var keyForID = 'sub'+sid+'_addonID'+aoID;
			toggleCardsOverlay(true, keyForID+'_card');
			let subAddOnForm = $('##'+keyForID+'_formWrapper');
			let subAddOnSummary = $('##'+keyForID+'_summary');
			let subAddOnCard = subAddOnSummary.closest('.addOnCards');
			
			subAddOnSummary.addClass('d-none');
			subAddOnForm.removeClass('d-none');
			
			subAddOnForm.find('input.subSelector:checked').each(function(){
				let subID = $(this).val();
				let innersubAddOns = $('##sub'+subID+'_addons');
				innersubAddOns.removeClass('d-none');
			});

			if (typeof subAddOnsCache == "object") {
				subAddOnCard.find('.subCardCancelBtn').removeClass('d-none');
			} else {
				subAddOnCard.find('.subCardCancelBtn').addClass('d-none');
			}

			return subAddOnCard;
		}
		function cancelSubAddOnChanges(addOnID,subID) {
			$('##sub'+mcsub_rootsubid+'_addons').replaceWith(subAddOnsCache.clone());
			defineEditHandlerForSubCards('reloadAddOn');
			validateOnLoadSubAddOns();
			prepTotalsAndNavigation(true);
			toggleCardsOverlay(false);
		}
		function confirmSubAddOn(aoID,sid,enableScroll){
			let subAddOnForm = $('##sub'+sid+'_addonID'+aoID+'_formWrapper');
			let subAddOnSummary = $('##sub'+sid+'_addonID'+aoID+'_summary');
			let subAddOnCard = subAddOnSummary.closest('.addOnCards');

			/* revert to orig rate amts for unchecked sub rate fields having alerts */
			removeRateAlerts(subAddOnForm);

			subAddOnForm.find('input.subSelector:checked').each(function() {
				let thisSubID = $(this).val();
				
				/* sub rate selected */
				if ($("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").length) {
					$("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").prop('disabled',false);
					let rfid = $("input.subRateRadio[rate-subscriptionid="+thisSubID+"]:checked").val();
					hideSubMainCardAlert(subAddOnForm.closest('.mainSubCard'),$(this).attr('id'));

					/* validate editable rate amts */
					let editableRateField = $('##newRateTotal_'+thisSubID+'_'+rfid);
					if (editableRateField.length) {
						doValidateRateAmt(editableRateField[0],false);
					}
				} else {
					showSubMainCardAlert(subAddOnForm.closest('.mainSubCard'),$(this).attr('id'),'Select a Rate.');
					return false;
				}
			});

			/* any alerts */
			let alertContainer = subAddOnForm.find('.alert:first');
			if (alertContainer.length) {
				if (enableScroll) {
					$('html, body').animate({
						scrollTop: alertContainer.offset().top - 175
					}, 750);
				}

				return false;
			}

			/* min/max check */
			validateAddOnSubMinMaxSelections(subAddOnCard.find('.subAddOnFormContainer'));
			
			let addOnFooterContainer = subAddOnCard.find('.addOnFooterContainer');
			if (subAddOnCard.find('.bg-light-danger').length) {
				addOnFooterContainer.find('.addOnBottomAlert').html('Please complete all required fields.').addClass('alert alert-danger');
				return false;
			} else {
				addOnFooterContainer.find('.addOnBottomAlert').html('').removeClass('alert alert-danger');
			}

			subAddOnSummary.removeClass('d-none');
			subAddOnForm.addClass('d-none');

			if (enableScroll) {
				$('html, body').animate({
					scrollTop: subAddOnCard.offset().top - 100
				}, 750);
			}

			updateSubCardSummary();
			calculateSubRateTotals();
			toggleCardsOverlay(false);
			return true;
		}
		function getSubRateTotals(useModifiedRates){
			let grandTotal = 0, strCardTotals = {}, totalDiscount = 0;
			
			if(useModifiedRates){
				$('##subSummaryTable input.modifiedRatePriceField').each(function() {
					grandTotal += Number(parseFloat($(this).val().replace(',','')).toFixed(2));
				});
				$('##frmManageSubs .subCouponRateFields').each(function() {
					totalDiscount += Number(parseFloat($(this).val().replace(',','')).toFixed(2));
				});
			}
			else {
				$('##rootSubWrapper,.addOnCardBody').each(function() {
					let subCardTotal = 0, subCardTotalDiscount = 0;
					let addOnID = $(this).data('addonid') ? $(this).data('addonid') : 0;
					let subID = $(this).data('subscriptionid');
					let subkey = addOnID > 0 ? 'sub'+subID+'_addonID'+addOnID : 'root';
					let strSubCardTotals = {};
					strCardTotals[subkey] = 0;

					$(this).find('input.subRateRadio:checked').each(function() {
						/* assoc sub not checked */
						let subInput = $('##'+$(this).data("linkedsubinputid"));
						if (!subInput.is(':checked')) return;

						/* is free sub */
						if (subInput.attr('data-isfreeaddonsub') == 1) return;

						let rfid = $(this).val();
						let freqCode = $(this).data('freq');
						let assocSubID = subInput.val();
						let rateID = $(this).data('rateid');

						if (!(freqCode in strSubCardTotals))  {
							strSubCardTotals[freqCode] = { freqName:$(this).data('freqname'), totalAmt:0 };
						}

						if ($('##newRateTotal_'+assocSubID+'_'+rfid).length) {
							let thisEditPriceField = $('##newRateTotal_'+assocSubID+'_'+rfid);
							let thisSubID = thisEditPriceField.data('subscriptionid');
							let thisRateWrapper = thisEditPriceField.closest('.sub'+thisSubID+'_rateWrapper');
							if (!thisRateWrapper.hasClass('alert')) {
								let thisRateAmt = Number(thisEditPriceField.val().replace(',',''));
								let thisRateTotalAmt = thisRateAmt * Number(thisEditPriceField.data('rateinstallments'));
								subCardTotal += Number(thisRateTotalAmt.toFixed(2));
								
								strSubCardTotals[freqCode].totalAmt += freqCode == 'F' ? thisRateTotalAmt : thisRateAmt;
							}

						} else {
							let thisRateAmt = Number(parseFloat($(this).attr("data-termprice")).toFixed(2));
							subCardTotal += thisRateAmt;
							strSubCardTotals[freqCode].totalAmt += freqCode == 'F' ? thisRateAmt : Number(parseFloat($(this).attr("data-price")).toFixed(2));
						}

						if ($('##subRateDiscount_'+assocSubID+'_'+rateID).length) {
							subCardTotalDiscount = Number(parseFloat(subCardTotalDiscount + Number($('##subRateDiscount_'+assocSubID+'_'+rateID).val())).toFixed(2));
						}
					});

					strCardTotals[subkey] = subCardTotal;
					grandTotal += subCardTotal;
					totalDiscount += subCardTotalDiscount;
				});
			}

			return { grandTotal:grandTotal, strCardTotals:strCardTotals, totalDiscount:totalDiscount, discountAppliedGrandTotal:Number(parseFloat(grandTotal - totalDiscount)).toFixed(2) };
		}
		function calculateSubRateTotals() {
			let grandTotal = 0, totalDiscount = 0;
			
			$('##rootSubWrapper,.addOnCardBody').each(function() {
				let subCardTotal = 0, subCardTotalDiscount = 0;
				let addOnID = $(this).data('addonid') ? $(this).data('addonid') : 0;
				let subID = $(this).data('subscriptionid');
				let subCard = addOnID > 0 ? $('##sub'+subID+'_addonID'+addOnID+'_card') : $('##rootSubWrapper');
				let selectedCount = $(this).find('input.subSelector:checked').length;
				let strSubCardTotals = {};

				$(this).find('input.subRateRadio:checked').each(function() {
					/* assoc sub not checked */
					let subInput = $('##'+$(this).data("linkedsubinputid"));
					if (!subInput.is(':checked')) return;

					/* is free sub */
					if (subInput.attr('data-isfreeaddonsub') == 1) return;

					let rfid = $(this).val();
					let freqCode = $(this).data('freq');
					let assocSubID = subInput.val();
					let rateID = $(this).data('rateid');

					if (!(freqCode in strSubCardTotals))  {
						strSubCardTotals[freqCode] = { freqName:$(this).data('freqname'), totalAmt:0 };
					}

					if ($('##newRateTotal_'+assocSubID+'_'+rfid).length) {
						let thisEditPriceField = $('##newRateTotal_'+assocSubID+'_'+rfid);
						let thisSubID = thisEditPriceField.data('subscriptionid');
						let thisRateWrapper = thisEditPriceField.closest('.sub'+thisSubID+'_rateWrapper');
						if (!thisRateWrapper.hasClass('alert')) {
							let thisRateAmt = Number(thisEditPriceField.val().replace(',',''));
							let thisRateTotalAmt = thisRateAmt * Number(thisEditPriceField.data('rateinstallments'));
							subCardTotal += Number(thisRateTotalAmt.toFixed(2));
							
							strSubCardTotals[freqCode].totalAmt += freqCode == 'F' ? thisRateTotalAmt : thisRateAmt;
						}

					} else {
						let thisRateAmt = Number(parseFloat($(this).attr("data-termprice")).toFixed(2));
						subCardTotal += thisRateAmt;
						strSubCardTotals[freqCode].totalAmt += freqCode == 'F' ? thisRateAmt : Number(parseFloat($(this).attr("data-price")).toFixed(2));
					}

					if ($('##subRateDiscount_'+assocSubID+'_'+rateID).length) {
						subCardTotalDiscount = Number(parseFloat(subCardTotalDiscount + Number($('##subRateDiscount_'+assocSubID+'_'+rateID).val())).toFixed(2));
					}
				});

				grandTotal += subCardTotal;
				totalDiscount += subCardTotalDiscount;

				let priceDisplay = '';
				if (selectedCount > 0 && Object.keys(strSubCardTotals).length) {
					let subCardFreqCode = Object.keys(strSubCardTotals)[0];
					
					if (strSubCardTotals[subCardFreqCode].totalAmt > 0) {
						priceDisplay = ' - $' + formatCurrency(strSubCardTotals[subCardFreqCode].totalAmt.toFixed(2));
						if (subCardFreqCode != 'F') priceDisplay += ' ' + strSubCardTotals[subCardFreqCode].freqName;
					} else if (mcsub_freeratedisplay.length) {
						priceDisplay = ' - ' + mcsub_freeratedisplay;
					}
				}

				subCard.find('.'+$(this).data('displaypriceelement')).html(priceDisplay);
			});

			$(".grandTotal").html('$' + formatCurrency(grandTotal.toFixed(2)));

			if (totalDiscount) {
				let discountAppliedGrandTotal = Number(parseFloat(grandTotal - totalDiscount)).toFixed(2);
				$('.grandTotal').html('$' + formatCurrency(discountAppliedGrandTotal));
				$('.actualSubTotal').html('$' + formatCurrency(grandTotal.toFixed(2)));
				$('.totalSubDiscount').html('$' + formatCurrency(totalDiscount.toFixed(2)));
				$('.subCouponAppliedContainer').removeClass('d-none');
				$('##subRegCartTotal').addClass('d-none');
			} else {
				$('.subCouponAppliedContainer').addClass('d-none');
				$('##subRegCartTotal').removeClass('d-none');
			}
		}
		function onFocusEditRateField(thisObj) {
			let subID = $(thisObj).data('subscriptionid');
			let rfid = $(thisObj).data('rfid');
			let rateRadio = $('##sub'+subID+'_rfid_'+rfid);
			if (!rateRadio.is(':checked')) {
				rateRadio.prop('checked',true).trigger('change');
			}
		}
		function validateRateAmt(thisObj,byPassDelay) {
			let isFinalizedChange = (event && event.key == 'Enter') || !$(thisObj).is(':visible');
			
			if (isFinalizedChange || byPassDelay) {
				doValidateRateAmt(thisObj,isFinalizedChange);
			} else {
				mca_delayFunc(1200).then(function() {
					doValidateRateAmt(thisObj,isFinalizedChange);
				});
			}
		}
		function doValidateRateAmt(thisObj,isFinalizedChange) {
			let thisRatePrice = formatCurrency($(thisObj).val()).replace('.00','');
			if (thisRatePrice == 0) $(thisObj).val(0);

			let minPrice = Number($(thisObj).data('ratemin'));
			let maxPrice = Number($(thisObj).data('ratemax'));
			let numInstallments = Number($(thisObj).data('rateinstallments'));
			let subID = $(thisObj).data('subscriptionid');
			let rfid = $(thisObj).data('rfid');
			let rateAmt = Number(thisRatePrice.replace(',',''));
			let totalAmt = rateAmt * numInstallments;

			if ($('##totalPrice'+rfid).length) {
				$('##totalPrice'+rfid).html('$'+formatCurrency(totalAmt));
			}

			if (minPrice > 0 || maxPrice > 0) {
				let errMsg = '';
				let errCode = '';
				let subRateContainer = $(thisObj).closest('.sub'+subID+'_rateWrapper');
				let subMainCard = $(thisObj).closest('.mainSubCard');
				
				if (minPrice > 0 && totalAmt < minPrice) {
					errMsg = 'Amount is below $'+formatCurrency(minPrice)+' Minimum';
					errCode = 'minprice';
				} else if (maxPrice > 0 && totalAmt > maxPrice) {
					errMsg = 'Amount exceeds $'+formatCurrency(maxPrice)+' Maximum';
					errCode = 'maxprice';
				}

				if (errMsg.length) {
					subRateContainer.find('.editRatePriceRange').addClass('d-none');
					subRateContainer.addClass('alert alert-danger');
					subRateContainer.attr('data-errcode',errCode);
					subRateContainer.find('.editRatePriceRangeErr').html(errMsg).removeClass('d-none');

					let titleErrMsg = '';
					if (errCode == 'minprice') {
						titleErrMsg = 'Current selections do not meet the required minimum amount of $' + formatCurrency(minPrice) + '.';
					} else if (errCode == 'maxprice') {
						titleErrMsg = 'Current selections exceed the required maximum amount of $' + formatCurrency(maxPrice) + '.';
					}

					showSubMainCardAlert(subMainCard,$(thisObj).attr('id'),titleErrMsg);

				} else {
					subRateContainer.find('.editRatePriceRange').removeClass('d-none');
					subRateContainer.removeClass('alert alert-danger');
					subRateContainer.attr('data-errcode','');
					subRateContainer.find('.editRatePriceRangeErr').html('').addClass('d-none');

					hideSubMainCardAlert(subMainCard,$(thisObj).attr('id'));
				}
			}

			removeRateAlerts($(thisObj).closest('.subsContainer'));
			prepTotalsAndNavigation(false);

			/* move focus away */
			if (isFinalizedChange) {
				$(thisObj).blur();
				return false;
			}
		}
		function onBlurRateAmt(thisObj) {
			let thisRatePrice = formatCurrency($(thisObj).val()).replace('.00','');
			$(thisObj).val(thisRatePrice);
		}
		function removeRateAlerts(formContainer) {
			let subMainCard = formContainer.closest('.mainSubCard');

			formContainer.find('.alert').each(function() {
				let thisRatePriceField = $(this).find('.editableRateFields');
				if (!thisRatePriceField.length) return;

				let assocRate = $('##sub'+thisRatePriceField.data('subscriptionid')+'_rfid_'+thisRatePriceField.data('rfid'));
				if (!assocRate.is(':checked')) {
					thisRatePriceField.val(thisRatePriceField.data('origrateamt'));
					$(this).removeClass('alert alert-danger');
					$(this).find('.editRatePriceRange').removeClass('d-none');
					$(this).find('.editRatePriceRangeErr').addClass('d-none');
					hideSubMainCardAlert(subMainCard,thisRatePriceField.attr('id'));
				}
			});
		}
		function chooseSub(thisObj){
			let thisID = $(thisObj).attr('id');
			let subID = $(thisObj).val();
			let addOnID = $(thisObj).data("addonid");
			let parentSubID = $(thisObj).data('parentsubscriptionid');
			let numRates = $("input.subRateRadio[rate-subscriptionid="+subID+"]").length;

			if(thisObj.checked) {
				/* checking a sub to automatically select the first sub rate */		
				if($("input.subRateRadio[rate-subscriptionid="+subID+"][data-linkedsubinputid="+thisID+"]:checked").length == 0) {
					$($("input.subRateRadio[rate-subscriptionid="+subID+"][data-linkedsubinputid="+thisID+"]")[0]).trigger("click");
				}

				validateAddOnSubSelections(parentSubID,addOnID,subID,true);

				/* show sub addons if any */
				$('##sub'+subID+'_addons').removeClass('d-none');

				/* show rate amt for a single rate sub and not a free sub */
				if ($(thisObj).attr('data-isfreeaddonsub') != 1) {
					$('.addOnSub'+subID+'SingleRateLabel').removeClass('d-none');
				}

				/* show edit price input box */
				if (numRates == 1) {
					$('.sub'+subID+'_rateWrapper').find('.editRatePrices').removeClass('d-none');
				}

			} else {
				validateAddOnSubSelections(parentSubID,addOnID,subID,false);

				/* uncheck selected rate */
				$("input.subRateRadio[rate-subscriptionid="+subID+"][data-linkedsubinputid="+thisID+"]:checked").prop('checked', false);
				
				/* uncheck sub-addon subs */
				$('##sub'+subID+'_addons').find('input.subSelector:checked').each(function() {
					let thisSubID = $(this).val();
					let thisAddOnID = $(this).data("addonid");
					uncheckSub(thisSubID,thisAddOnID);
				});

				uncheckSub(subID,addOnID);
			}

			<cfif local.useAccrualAcct>
				refreshRecognitionDatesForSub(subID,thisObj.checked);
			</cfif>

			prepTotalsAndNavigation(false);
		}
		function uncheckSub(subID,addOnID) {
			let numRates = $("input.subRateRadio[rate-subscriptionid="+subID+"]").length;

			$('.sub'+subID+'_addons input.subSelector:checked').prop('checked', false);
			$('.sub'+subID+'_addons input.subRateRadio:checked').prop('checked', false);
			$('.sub'+subID+'_addons input.subSelector').prop('disabled', false);

			$('##sub'+subID+'_addons, .addOnSub'+subID+'SingleRateLabel').addClass('d-none');
			$('##sub'+subID+'_addons').find('.addOnSubSingleRateLabel').addClass('d-none');
			$('.sub'+subID+'RateAmtDisp').removeClass('d-none');
			$('##sub'+subID+'_addons').find('.subRateAmtDisp').removeClass('d-none');

			/* hide edit price input box */
			let subRateContainer = $('.sub'+subID+'_rateWrapper');
			if (numRates == 1 && subRateContainer.length && subRateContainer.find('.editRatePrices').length) {
				let subMainCard = subRateContainer.closest('.mainSubCard');
				subRateContainer.removeClass('alert alert-danger');
				subRateContainer.attr('data-errcode','');
				subRateContainer.find('.editRatePriceRange').removeClass('d-none');
				subRateContainer.find('.editRatePriceRangeErr').html('').addClass('d-none');
				subRateContainer.find('.editableRateFields').val(subRateContainer.find('.editableRateFields').data('origrateamt'));
				subRateContainer.find('.editRatePrices').addClass('d-none');
				hideSubMainCardAlert(subMainCard,subRateContainer.find('.editableRateFields').attr('id'));
			}
		}
		/* alerts */
		function manageSubsAlert() {
			let arrErrAddOns = [];
			$('##sub'+mcsub_rootsubid+'_addons').find('.mainSubCard').each(function() {
				if ($(this).attr('data-errfields').length) {
					arrErrAddOns.push({ name:$(this).data('setname'), jumpto:$(this).attr('id') });
				}
			});

			if (arrErrAddOns.length) {
				let errMsg = '<div class="mb-2">The following Subscription Add-Ons need your attention. <span class="font-size-xs">(Click the Add-On for more details)</span></div><ul>';
				arrErrAddOns.forEach(function(strAddOn) {
					errMsg += '<li><a href="##" onclick="jumpToSubCard(\''+strAddOn.jumpto+'\');return false;">'+strAddOn.name+'</a></li>';
				});
				errMsg += '</ul>';
				showSubsAlert(errMsg);
			} else {
				hideSubsAlert();
			}
		}
		function showSubsAlert(msg) {
			$('##manageSubsHeaderAlert').html(msg).removeClass('d-none');
		}
		function hideSubsAlert() {
			$('##manageSubsHeaderAlert').html('').addClass('d-none');
		}
		function showSubMainCardAlert(subMainCard,errFld,errMsg) {
			let subMainCardTitleContainer = subMainCard.find('.'+subMainCard.data('linkedtitlecontainer'));
			let subMainCardTitleErrContainer = subMainCard.find('.'+subMainCard.data('linkedtitleerrorcontainer'));
			let subCardSummary = $('##'+subMainCard.data('linkedsummarycontainer'));
			let subCardForm = $('##'+subMainCard.data('linkedformcontainer'));

			addSubMainCardAlertElement(subMainCard,errFld);

			if (subMainCard.attr('data-errfields').length) {
				if (errMsg.length) {
					subMainCardTitleErrContainer.html(errMsg);
					subCardForm.find('.card-header').removeClass('bg-light').addClass('bg-light-danger');
				}
				subMainCard.addClass('border-danger');
			}

			manageSubsAlert();
		}
		function hideSubMainCardAlert(subMainCard,errFld) {
			let subMainCardTitleContainer = subMainCard.find('.'+subMainCard.data('linkedtitlecontainer'));
			let subMainCardTitleErrContainer = subMainCard.find('.'+subMainCard.data('linkedtitleerrorcontainer'));

			removeSubMainCardAlertElement(subMainCard,errFld);
			
			if (!subMainCard.attr('data-errfields').length) {
				subMainCardTitleErrContainer.html('');
				subMainCard.find('.card-header').addClass('bg-light').removeClass('bg-light-danger');
				subMainCard.removeClass('border-danger');
			}

			manageSubsAlert();
		}
		function addSubMainCardAlertElement(subMainCard,errFld) {
			let subMainCardErrFlds = subMainCard.attr('data-errfields') || '';
			let arrErrFlds = subMainCardErrFlds.length ? subMainCardErrFlds.split(',') : [];
			
			if (errFld.length && arrErrFlds.indexOf(errFld) == -1) {
				arrErrFlds.push(errFld);
			}
			subMainCard.attr('data-errfields',arrErrFlds.join(','));
		}
		function removeSubMainCardAlertElement(subMainCard,errFld) {
			let subMainCardErrFlds = subMainCard.attr('data-errfields') || '';
			let arrErrFlds = subMainCardErrFlds.length ? subMainCardErrFlds.split(',') : [];
			let index = arrErrFlds.indexOf(errFld);
			if (index > -1) {
				arrErrFlds.splice(index, 1);
			}
			subMainCard.attr('data-errfields',arrErrFlds.join(','));
		}
		function updateSubCardSummary() {
			let rootSubRate = $('##rootSubForm').find('input.subRateRadio:checked');
			if (! rootSubRate.length) return false;
			
			let rootSubRateRFID = rootSubRate.val() || 0;
			let editableRootSubRateField = $('##newRateTotal_'+mcsub_rootsubid+'_'+rootSubRateRFID);
			let editableRootSubRate = editableRootSubRateField.length == 1;

			let strRootSub = {
				subid:mcsub_rootsubid,
				subname:$('##sub'+mcsub_rootsubid).data("subscriptionname"),
				rateName:rootSubRate.data('ratename'),
				rfid:rootSubRateRFID,
				freqCode:rootSubRate.data('freq'),
				freqName:rootSubRate.data('freqname'),
				setid:0,
				setname:'',
				ordernum:1,
				editableRate:editableRootSubRate
			};

			if (editableRootSubRate) {
				let rootSubRateAmt = editableRootSubRateField.val().replace(',','');
				let totalRootSubRateAmt = Number(rootSubRateAmt) * Number(editableRootSubRateField.data('rateinstallments'));
				
				strRootSub.rateAmt = Number(rootSubRateAmt);
				strRootSub.totalRateAmt = Number(totalRootSubRateAmt.toFixed(2));
				strRootSub.rateMin = editableRootSubRateField.data('ratemin');
				strRootSub.rateMax = editableRootSubRateField.data('ratemax');
			}

			$('##rootSubRateInfo').html(getSubVerbose(strRootSub,0,0));
			if ($('##rootSubForm').find('input.subRateRadio').length > 1 || ($('##rootSubForm').find('input.subRateRadio').length && editableRootSubRate)) {
				$('##rootSubEditBtn').html('<span><i class="fa-solid fa-pencil mr-1"></i> Edit</span>').removeClass('d-none');
			} else {
				$('##rootSubEditBtn').remove();
			}
			
			$('.addOnCardBody').each(function() {
				let arrSubs = [], strSets = {}, childSetID = $(this).data('childsetid');

				$(this).find('input.subSelector:checked').each(function() {
					let setID = $(this).data('setid');
					let selectedSubID = $(this).val();
					let selectedSubRate = $('input.subRateRadio[name="sub'+selectedSubID+'_rfid"]:checked');
					let rateName = selectedSubRate.data('ratename');
					let rfid = selectedSubRate.val();
					let editableRateField = $('##newRateTotal_'+selectedSubID+'_'+rfid);
					let editableRate = editableRateField.length == 1;

					if (!(setID in strSets)) strSets[setID] = 1;
					else strSets[setID]++;

					let strSub = {
						subid:selectedSubID,
						subname:$(this).attr("data-subscriptionname"),
						rateName:rateName,
						rfid:rfid,
						setid:setID,
						freqCode:selectedSubRate.data('freq'),
						freqName:selectedSubRate.data('freqname'),
						setname:$('##subSet'+setID).data('setname'),
						ordernum:strSets[setID],
						inlineaddon:$('##subSet'+setID).data('inlineaddon'),
						editableRate:editableRate
					};

					if (editableRate) {
						let rateAmt = editableRateField.val().replace(',','');
						let totalRateAmt = Number(rateAmt) * Number(editableRateField.data('rateinstallments'));
						
						strSub.rateAmt = Number(rateAmt);
						strSub.totalRateAmt = Number(totalRateAmt.toFixed(2));
						strSub.rateMin = editableRateField.data('ratemin');
						strSub.rateMax = editableRateField.data('ratemax');
					}
					
					arrSubs.push(strSub);
				});

				let addOnID = $(this).data('addonid');
				let subID = $(this).data('subscriptionid');
				let subAddOnCard = $('##sub'+subID+'_addonID'+addOnID+'_card');
				let subAddOnSummary = $('##sub'+subID+'_addonID'+addOnID+'_summary').find('.addOnSummary');
				subAddOnSummary.html('');

				arrSubs.forEach(function(strSub) {
					let paddingLeft = strSub.inlineaddon && strSub.inlineaddon == 1 ? 15 : 0;
					let paddingTop = 0;
					
					if (strSub.setid != childSetID) {
						if (strSub.ordernum == 1) {
							subAddOnSummary.append('<div class="font-weight-bold mb-1 font-size-md" style="padding-left:'+paddingLeft+'px;">'+strSub.setname+'</div>');
						}
						paddingLeft += 10;
					}
					subAddOnSummary.append(getSubVerbose(strSub,paddingLeft,paddingTop));
				});

				subAddOnCard.find('.subCardEditBtn').html(arrSubs.length ? '<span><i class="fa-solid fa-pencil mr-1"></i> Edit</span>' : '<span><i class="fa-solid fa-plus mr-1"></i> Add</span>').removeClass('d-none');
			});

			/* selected add-on subs count */
			$('.subAddOnFormContainer').each(function() {
				let addOnID = $(this).data('addonid');
				let inputName = 'subAddOn'+addOnID;
				let numSubsSelected = $('input.subSelector[name="'+inputName+'"]:checked').length;
				$('.selectedSubsCount'+addOnID).html('(' + numSubsSelected + ' selected)');
			});
		}
		function formatCurrency(num) {
			num = num.toString().replace(/\$|\,/g,'');
			if(isNaN(num)) num = "0";
			num = Math.abs(num);
			sign = (num == (num = Math.abs(num)));
			num = Math.floor(num*100+0.50000000001);
			cents = num%100;
			num = Math.floor(num/100).toString();
			if(cents<10) cents = "0" + cents;
			for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
			return (((sign)?'':'-') + num + '.' + cents);
		}
		function showSubsFooterAlert(msg) {
			$('##manageSubsFooterAlert').html(msg).removeClass('d-none');
		}
		function hideSubsFooterAlert() {
			$('##manageSubsFooterAlert').html('').addClass('d-none');
		}
		function hasErrorsInSub() {
			let hasErr = false;
			$('.mainSubCard').each(function() {
				hasErr = $(this).attr('data-errfields').length > 0;
				if (hasErr) return false;
			});
			return hasErr;
		}
		function enableContinueBtn() {
			if (hasErrorsInSub()) {
				disableContinueBtn();
			} else {
				doEnableContinueBtn();
			}
		}
		function doEnableContinueBtn() {
			if(top.$('##btnMCModalSave').hasClass('d-none')) top.$('##btnMCModalSave').removeClass('d-none');
			top.$('##btnMCModalSave').prop('disabled',false);
			hideSubsFooterAlert();
		}
		function disableContinueBtn() {
			top.$('##btnMCModalSave').prop('disabled',true);
		}
		function getSubVerbose(strSub,paddingLeft,paddingTop) {
			let subHTML = '';

			if (strSub.editableRate) {
				subHTML += '<div class="d-flex flex-wrap no-gutters" style="padding-left:'+paddingLeft+'px;padding-top:'+paddingTop+'px;">';
				if (strSub.subid == mcsub_rootsubid) {
					subHTML += '<div class="col-auto pr-5">' + strSub.rateName + '</div>';
				} else {
					subHTML += '<div class="col-auto pr-5">' + strSub.subname + '</div>';
				}
				if (strSub.rateMin > 0 || strSub.rateMax > 0) {
					subHTML += '<div class="col-auto text-dim">';
					if (strSub.rateMin > 0 && strSub.rateMax > 0) {
						subHTML += '$' + formatCurrency(strSub.rateMin) + ' Min - $' + formatCurrency(strSub.rateMax) + ' Max';
					} else if (strSub.rateMin > 0) {
						subHTML += '$' + formatCurrency(strSub.rateMin) + ' Minimum';
					} else {
						subHTML += '$' + formatCurrency(strSub.rateMax) + ' Maximum';
					}
					subHTML += '</div>';
				}
				subHTML += '</div>';
				subHTML += '<div class="d-flex mb-2 align-items-center no-gutters" style="padding-left:'+paddingLeft+'px;padding-top:'+paddingTop+'px;">';
				subHTML += '<div class="col-auto '+(strSub.freqCode != 'F' ? ' pt-3' : '')+'">';
				subHTML += '<div class="input-group flex-nowrap"><div class="input-group-prepend"><span class="input-group-text px-2">$</span></div><div class="form-group mb-0"><input type="text" class="form-control" value="'+formatCurrency(strSub.rateAmt)+'" style="background-color:##f2f2f2 !important;color:##000 !important;" size="10" disabled></div></div>';

				if (strSub.freqCode != 'F') {
					subHTML += '<div class="font-size-sm text-dim text-center">Total: $'+formatCurrency(strSub.totalRateAmt)+'</div>';
				}
				subHTML += '</div>';

				if (strSub.freqCode != 'F') {
					subHTML += '<div class="col-auto pl-2">'+strSub.freqName+'</div>';
				}
				subHTML += '</div>';

			} else {
				subHTML += '<div class="d-flex align-items-center mb-2 flex-wrap no-gutters" style="padding-left:'+paddingLeft+'px;padding-top:'+paddingTop+'px;">';
				if (strSub.subid == mcsub_rootsubid) {
					subHTML += '<div class="col-auto">' + strSub.rateName + '</div>';
				} else if (strSub.setname != strSub.subname) {
					subHTML += '<div class="col-auto">' + strSub.subname + '</div>';
				}
				subHTML += '</div>';
			}

			return subHTML;
		}
		function prepTotalsAndNavigation(enchkbtn) {
			subCouponHandler().then(function() {
				calculateSubRateTotals();
				updateSubCardSummary();

				if (enchkbtn) {
					enableContinueBtn();
				}
			}).catch(function(e) {
				console.log(e);
			});
		}
		function initManageSubs() {
			top.$('##btnMCModalSave').addClass('d-none');
			defineEditHandlerForSubCards('init');
			<cfif local.hasRootSubRatesCount eq 1>
				let arrCheckedRadioObj = $('##rootSubWrapper input.subRateRadio:checked');
				if(arrCheckedRadioObj.length){
					chooseRate(arrCheckedRadioObj[0]);
				}
			<cfelse>
				editRootSub();
				prepTotalsAndNavigation(true);
			</cfif>
			
			/* prevent enter key submit */
			$('##frmManageSubs').on('keydown', function(e) {
				if (e.keyCode == 13) e.preventDefault();
			});

			Handlebars.registerPartial('mc_subRateRowHTML', $('##mc_subRateRowHTML').html());
			Handlebars.registerPartial('mc_subSelectionSummaryHTML', $('##mc_subSelectionSummaryHTML').html());
		}
		function subCouponHandler() {
			return new Promise(function(resolve,reject) {
				let result = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						resetCouponSubs();

						if (r.offercoupon) {
							$('.subCouponContainer').removeClass('d-none');
						} else if (r.couponapplied) {
							applyCouponToSubs(r);
						}

					} else {
						if (r.success && r.success.toLowerCase() == 'false' && r.couponresponse) {
							$('.couponCodeResponse').html(r.couponresponse).show();
						} else {
							$('.couponCodeResponse').html('Unable to apply coupon code. Try again.').show();
						}
					}

					resolve();
				};

				let arrQualifiedSubs = getCouponQualifiedSubs();
				let objParams = { memberID:mcsub_mid, rootSubscriberID:0, subs:JSON.stringify(arrQualifiedSubs), isAdmin:1 };
				TS_AJX('SUBREG','subCouponHandler',objParams,result,result,10000,result);
			}).catch(function(e) {
				console.log(e);
			});
		}
		function getCouponQualifiedSubs() {
			let arrQualifiedSubs = [];

			$('##rootSubWrapper,.addOnCardBody').each(function() {
				let addOnID = $(this).data('addonid') ? $(this).data('addonid') : 0;
				let subID = $(this).data('subscriptionid');
				let subCard = addOnID > 0 ? $('##sub'+subID+'_addonID'+addOnID+'_card') : $('##rootSubWrapper');
				let selectedCount = $(this).find('input.subSelector:checked').length;
				
				$(this).find('input.subRateRadio:checked').each(function() {
					/* assoc sub not checked */
					let subInput = $('##'+$(this).data("linkedsubinputid"));
					if (!subInput.is(':checked')) return;

					/* is free sub */
					if (subInput.attr('data-isfreeaddonsub') == 1) return;

					let rfid = $(this).val();
					let freqCode = $(this).data('freq');
					let assocSubID = subInput.val();
					let rateID = $(this).data('rateid');
					let thisSubPrice = 0;

					if ($('##newRateTotal_'+assocSubID+'_'+rfid).length) {
						let thisEditPriceField = $('##newRateTotal_'+assocSubID+'_'+rfid);
						let thisSubID = thisEditPriceField.data('subscriptionid');
						let thisRateWrapper = thisEditPriceField.closest('.sub'+thisSubID+'_rateWrapper');
						if (!thisRateWrapper.hasClass('alert')) {
							let thisRateAmt = Number(thisEditPriceField.val().replace(',',''));
							let thisRateTotalAmt = thisRateAmt * Number(thisEditPriceField.data('rateinstallments'));
							thisSubPrice = Number(thisRateTotalAmt);
						}

					} else {
						let thisRateAmt = Number(parseFloat($(this).attr("data-termprice")).toFixed(2));
						thisSubPrice = Number(thisRateAmt);
					}

					arrQualifiedSubs.push({ subscriptionid:assocSubID, rateid:rateID, parentsubscriptionid:subInput.data('parentsubscriptionid'), amt:thisSubPrice, numinstallments:Number($(this).data('rateinstallments')) });
				});
			});

			return arrQualifiedSubs;
		}
		function resetCouponSubs() {
			$('.subCouponContainer').addClass('d-none');
			$('.btnApplyCouponCode').prop('disabled',false).text('Apply');
			$('.couponCode').val('');
			$('input.subCouponRateFields').remove();
			$('.couponCodeResponse').html('').hide();
			$('.discountAppliedSubTotal,.actualSubTotal,.totalSubDiscount').html('');
			$('.btnRemoveCoupon').prop('disabled',false).text('Remove Promo Code');
			$('.subCouponAppliedContainer').addClass('d-none');
			$('##subRegCartTotal').removeClass('d-none');
			updateSubCardSummaryTotals();
		}
		function applyCouponToSubs(obj) {
			for (let subscriptionID in obj.strsubprice) {
				let discount = Number(obj.strsubprice[subscriptionID].discount);
				if (discount) {
					let rateID = obj.strsubprice[subscriptionID].rateid;
					$('##frmManageSubs').append('<input type="hidden" name="subRateDiscount_'+subscriptionID+'_'+rateID+'" id="subRateDiscount_'+subscriptionID+'_'+rateID+'" class="subCouponRateFields" data-subscriptionid="'+subscriptionID+'" data-discountperinstallment="'+obj.strsubprice[subscriptionID].discountperinstallment+'" value="'+discount+'">');
				}
			}
			$('.subCouponRedeemDetail').html(obj.redeemdetail);
			updateSubCardSummaryTotals();
		}
		function validateCouponCode() {
			var validateResult = function(r) {
				$('.btnApplyCouponCode').prop('disabled',false).text('Apply');
				$('.couponCode').val('');

				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.isvalidcoupon) {
						$('.subCouponContainer').addClass('d-none');
						let enableContinueBtn = $('##formStep').val() == 1;
						prepTotalsAndNavigation(enableContinueBtn);
					} else {
						$('.couponCodeResponse').html(r.couponresponse).show();
					}
				} else {
					if (r.couponresponse) {
						$('.couponCodeResponse').html(r.couponresponse).show();
					} else {
						$('.couponCodeResponse').html('Unable to apply coupon code. Try again.').show();
					}
				}
			};

			let couponCode = $('.subCouponContainer:visible').find('input[name="couponCode"]').val().trim();

			if (couponCode.length) {
				$('.btnApplyCouponCode').prop('disabled',true).text('Applying...');
				$('.couponCodeResponse').html('').hide();

				let arrQualifiedSubs = getCouponQualifiedSubs();
				let objParams = { couponCode:couponCode, memberID:mcsub_mid, rootSubscriberID:0, subs:JSON.stringify(arrQualifiedSubs), isAdmin:1 };
				TS_AJX('SUBREG','validateCouponCode',objParams,validateResult,validateResult,10000,validateResult);
			} else {
				validateResult({ success:'false', couponresponse:'Invalid Promo Code' });
			}
		}
		function removeAppliedCoupon() {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					resetCouponSubs();
					prepTotalsAndNavigation(true);
				} else {
					$('.btnRemoveCoupon').prop('disabled',false).text('Remove Promo Code');
					alert('Unable to remove applied coupon. Try again.');
				}
			};

			$('.btnRemoveCoupon').prop('disabled',true).text('Removing...');
			let objParams = { rootSubscriberID:0, mid:mcsub_mid, topSubID:mcsub_rootsubid };
			TS_AJX('SUBREG','removeAppliedCoupon',objParams,removeResult,removeResult,10000,removeResult);
		}

		$(function() {
			initManageSubs();
		});
	</script>
	<style>
		.mcsubs-active-card-body {overflow-y:auto;}
		.bg-light-danger {color: ##841e30!important;background-color: ##fed6da!important;}
		.card-disabled-overlay {position: relative;pointer-events: none;opacity: 0.5;filter: grayscale(70%);}
		##finalButtonsContainer .btnFinals {border-width:1px !important;}
		.btnFinals:disabled {opacity: 0.6;color: darkgrey;}
		##paymentScheduleTable{max-width:675px;}
		.field-header-label { font-size: .83125rem; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.headCode#">