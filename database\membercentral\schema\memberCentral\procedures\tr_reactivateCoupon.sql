CREATE PROC dbo.tr_reactivateCoupon
@siteID int,
@couponID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @couponCode varchar(15), @couponSiteResourceID int, @operation varchar(12), @oldStatus char(1);
	SELECT @orgID = orgID from dbo.sites where siteID = @siteID;	
	
	SELECT @couponSiteResourceID = siteResourceID, @couponCode = couponCode, @oldStatus = [status]
	FROM dbo.tr_coupons
	WHERE siteID = @siteID
	AND couponID = @couponID;

	IF @oldStatus = 'I' BEGIN
		BEGIN TRAN;
			UPDATE dbo.tr_coupons
			SET [status] = 'A'
			WHERE siteID = @siteID
			AND couponID = @couponID;

			UPDATE dbo.cms_siteResources
			SET siteResourceStatusID = 1
			WHERE siteResourceID = @couponSiteResourceID;

			SET @operation = 'reactivated';

			INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
			SELECT '{ "c":"auditLog", "d": { 
				"AUDITCODE":"COUPON", 
				"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ', 
				"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ', 
				"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
				"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
				"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars('Coupon [' + @couponCode + '] '+ @operation +'.'),'"','\"') + '" } }';
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
