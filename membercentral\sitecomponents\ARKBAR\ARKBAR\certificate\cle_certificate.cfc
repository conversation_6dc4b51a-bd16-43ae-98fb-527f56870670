<cfcomponent output="false">

    <cffunction name="getCertMergeCodes" access="public" output="false" returntype="struct">
        <cfscript>
            var strCertMergeCodes = {
                "member": "prefix,firstname,middlename,lastname,suffix,professionalsuffix,Supreme Court ID"
            };
            return strCertMergeCodes;
        </cfscript>
    </cffunction>

    <cffunction name="generateCertBody" access="public" output="false" returntype="string">
        <cfargument name="strCertMergeCodes" type="struct" required="true">
		<cfset var local = structNew()>
        <cfset local.objTSTZ = CreateObject("component", "model.system.platform.tsTimeZone")>
        <cfset local.siteTimeZoneID = application.objSiteInfo.getSiteInfo(arguments.strCertMergeCodes.certificate.orgcode).defaultTimeZoneID>
        <cfset local.siteTimeZoneCode = local.objTSTZ.getTZCodeFromTZID(timeZoneID=local.siteTimeZoneID)>
        <cfset local.siteTimeZoneAbbr = local.objTSTZ.getTZFromTZID(timeZoneID=local.siteTimeZoneID)>

        <!--- Convert completion date to site timezone --->
        <cfset local.completionDateInSiteTimezone = arguments.strCertMergeCodes.event.qryEventTimes.startTime>
        <cfif local.siteTimeZoneCode neq "US/Central">
            <cfset local.completionDateInSiteTimezone = local.objTSTZ.convertTimeZone(
                dateToConvert=arguments.strCertMergeCodes.event.qryEventTimes.startTime,
                fromTimeZone='US/Central',
                toTimeZone=local.siteTimeZoneCode
            )>
        </cfif>

        <!--- Calculate credit values --->
        <cfset local.generalCredits = 0>
        <cfset local.ethicsCredits = 0>
        <cfset local.adLitemDRCredits = 0>
        <cfset local.adLitemDNCredits = 0>
		<cfset local.possibleGeneralCredits = 0>
        <cfset local.possibleEthicsCredits = 0>
        <cfset local.possibleAdLitemDRCredits = 0>
        <cfset local.possibleAdLitemDNCredits = 0>

        <cfloop query="arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate">
            <cfif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditType eq 'General'>
                <cfset local.generalCredits = local.generalCredits + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
            <cfelseif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditType eq "Ethics">
                <cfset local.ethicsCredits = local.ethicsCredits + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
            <cfelseif FindNoCase("Ad Litem DR", arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditType)>
                <cfset local.adLitemDRCredits = local.adLitemDRCredits + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
            <cfelseif FindNoCase("Ad Litem DN", arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditType)>
                <cfset local.adLitemDNCredits = local.adLitemDNCredits + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
            </cfif>
        </cfloop>
		
		<cfloop query="arguments.strCertMergeCodes.credit.qryPossibleCredits">
            <cfif arguments.strCertMergeCodes.credit.qryPossibleCredits.creditType eq 'General'>
                <cfset local.possibleGeneralCredits = local.possibleGeneralCredits + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue>
            <cfelseif arguments.strCertMergeCodes.credit.qryPossibleCredits.creditType eq "Ethics">
                <cfset local.possibleEthicsCredits = local.possibleEthicsCredits + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue>
            <cfelseif FindNoCase("Ad Litem DR", arguments.strCertMergeCodes.credit.qryPossibleCredits.creditType)>
                <cfset local.possibleAdLitemDRCredits = local.possibleAdLitemDRCredits + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue>
            <cfelseif FindNoCase("Ad Litem DN", arguments.strCertMergeCodes.credit.qryPossibleCredits.creditType)>
                <cfset local.possibleAdLitemDNCredits = local.possibleAdLitemDNCredits + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue>
            </cfif>
        </cfloop>

        <!--- Get registrant name --->
        <cfsavecontent variable="local.registrantName">
            <cfoutput>#UCASE(arguments.strCertMergeCodes.member.firstname & " " & arguments.strCertMergeCodes.member.middlename & " " & arguments.strCertMergeCodes.member.lastname & " " & arguments.strCertMergeCodes.member.suffix)#</cfoutput>
        </cfsavecontent>

        <cfsavecontent variable="local.certBody">
            <cfoutput>
				<html>
				<head>
				<title>Uniform Certificate of Attendance</title>
				<style>
				* { padding: 0; margin: 0; }
				html, body {
				    width: 8.5in;
				    font-family: Calibri, sans-serif;
				    font-size: 12pt;
				    color: ##000;
				    padding: 0;
				    margin: 0;
				}
				.certificate-wrapper {
				    margin: 0.5in;
				    width: 7.5in;
				    padding: 0;
				}
				@page {
				    size: 8.5in 11in;
				    margin: 0.5in;
				}
				.header {
				    text-align: center;
				    margin-bottom: 10px;
				}
				.title {
				    font-weight: bold;
				    font-size: 14pt;
				    margin-bottom: 10px;
				    margin-top: 10px;
				}
				.section {
				    margin-bottom: 5px;
				}
				.field-group {
				    margin-bottom: 5px;
				    padding-left: 220px;
				}
				.underline {
				    border-bottom: 1px solid ##000;
				    display: inline-block;
				    width: 100%;
				    padding-left: 10px;
				}
				.signature-field {
				    display: inline-block;
				    width: 100%;
				    margin-right: 5%;
				    border-bottom: 1px solid ##000;
				    height: 30px;
				    margin-bottom: 5px;
				}
				</style>
				</head>
				<body>

				<div class="certificate-wrapper">
				<div class="header">
				    <div class="title">Uniform Certificate of Attendance</div>
				    <div>To be filed within fifteen (15) days upon completion of the program to The Office<br/>
				    of Professional Programs at 501 Woodlane St., Suite 303, Little Rock, AR<br/>
				    72201-1026 or e-<NAME_EMAIL>.</div>
				</div>

				<div class="section">
				    <strong>Sponsoring Organization:</strong> Arkansas Bar Association
				</div>

				<div class="section">
				    <strong>Activity Title:</strong> #arguments.strCertMergeCodes.event.qryEventMeta.eventContentTitle#
				</div>

				<div class="section">
				    <strong>Date:</strong> #DateFormat(local.completionDateInSiteTimezone, "mmmm d, yyyy")# at #TimeFormat(local.completionDateInSiteTimezone, "h:mm tt")# #local.siteTimeZoneAbbr#
				</div>
				<cfif LEN(arguments.strCertMergeCodes.event.qryEventMeta.locationContentTitle)>
				<div class="section">
				    <strong>Location:</strong> #arguments.strCertMergeCodes.event.qryEventMeta.locationContentTitle#
				</div>
				</cfif></cfoutput></cfsavecontent>

				        <cfif len(trim(arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.courseApproval ?: ""))>
				            <cfsavecontent variable="local.courseApprovalSection">
				                <cfoutput>
				<div class="section">
				    <strong>Course Number:</strong> #arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.courseApproval#
				</div>
				                </cfoutput>
				            </cfsavecontent>
				            <cfset local.certBody = local.certBody & local.courseApprovalSection>
				        </cfif>

				        <cfsavecontent variable="local.creditsSection">
				            <cfoutput>

				<div class="section">
				   This program is eligible for a total of:

				    <div class="field-group">
				        <span>
				            #NumberFormat(local.possibleGeneralCredits, "0.0")#
				        </span>
				        General credits based on a full 60-minute hour
				    </div>

				    <div class="field-group">
				        <span>
				            #NumberFormat(local.possibleEthicsCredits, "0.0")#
				        </span>
				        Ethics credits based on a full 60-minute hour
				    </div>

				    <div class="field-group">
				        <span>
				            #NumberFormat(local.possibleAdLitemDRCredits, "0.0")#
				        </span>
				        Ad Litem/DR credits based on a full 60-minute hour
				    </div>

				    <div class="field-group">
				        <span>
				            #NumberFormat(local.possibleAdLitemDNCredits, "0.0")#
				        </span>
				        Ad Litem/DN credits based on a full 60-minute hour
				    </div>
				</div>

				<strong>NOTE:</strong> Arkansas is a 60-minute CLE state and all programs will be reviewed based on a full
				60-minute. Introductory remarks, keynote addresses, business meetings, breaks
				and meals (with no speaker or presenter), receptions, etc., are not included in the
				computation of credit.

				<div class="title">TO BE COMPLETED BY ATTORNEY:</div>

				<p>By signing below, I certify that I attended the activity described above and am entitled to claim
				#NumberFormat(local.generalCredits, "0.0")#
				 General credit hours,

				#NumberFormat(local.ethicsCredits, "0.0")#
				 Ethics credits,

				#NumberFormat(local.adLitemDRCredits, "0.0")#
				 Ad Litem/DR credit and/or

				#NumberFormat(local.adLitemDNCredits, "0.0")#
				 Ad Litem/DN credits.</p>

				<div style="display: inline-block; width: 35%; vertical-align: top;">
				    <div class="underline">#trim(local.registrantName)#</div>
				    <div style="padding-bottom: 5px;">Attorney Name (Print):</div>
				    <div class="signature-field"></div>
				    <div style="padding-bottom: 5px;">Attorney Signature</div>
				</div>

				<div style="display: inline-block; width: 50%; margin-left: 2%; vertical-align: top;">
				    <div class="underline">#arguments.strCertMergeCodes.member['Supreme Court ID']#</div>
				    <div style="padding-bottom: 5px; white-space: nowrap;">Membership, Registration or Supreme Court Number</div>
				    <div class="signature-field"></div>
				    <div style="padding-bottom: 5px;">Date</div>
				</div>

				<div style="padding-bottom: 5px; width: 50%;">
				    <div class="signature-field"></div>
				    <div>State where credits are to be registered</div>
				</div>
				<strong>Note:</strong> Complete a certificate for each state in which you are required to file. Rules for CLE in some states
				    require the provider to file attendance with the regulator as a service to lawyers. Please confirm
				    jurisdictional reporting requirements with the provider or state regulator.

				<div style="text-align: left; padding-left: 400px;">
				    <strong>Acknowledged by:</strong><br/>
				    <img src="#arguments.strCertMergeCodes.certificate.imagesurl#kfrye_signature.jpeg" style="max-height: 40px;" />
				    <div style="border-bottom: 1px solid ##000; width: 150px;"></div>
				    <strong>Sponsor Representative</strong>
				</div>

				</div>

				</body>
				</html>
            </cfoutput>
        </cfsavecontent>

        <cfset local.certBody = local.certBody & local.creditsSection>

        <cfreturn local.certBody>
    </cffunction>

</cfcomponent>
