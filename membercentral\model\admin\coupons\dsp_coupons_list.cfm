<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script type="text/javascript">
		var #ToScript(local.permsGotoLink,"mca_perms_link")#
		let listCouponsTable;
		
		function showCouponFilter(){
			if (!$('##divCouponsFilterForm').is(':visible')) {
				$('##divCouponsFilterForm').show();
			}
		}
		function resetCouponsFilter() {
			$('##frmCouponsFilter')[0].reset();
			filterCoupons();
		}	
		function filterCoupons() {
			listCouponsTable.draw();
			return false;
        }	
		function clearDateRangeFields(f,t) {
			$('##'+f).val('');
			$('##'+t).val('');
			return false;
		}
		function editCoupon(id,u) {
			$('##divCouponsGridContainer,div.coupontool').hide();
			
			if (Number(id) == 0) {
				var loadHTML = '<h5>Add New Coupon</h5><div><img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...</div><br/><br/>';
			} else {
				var loadHTML = '<h5>Edit Coupon</h5><div><img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...</div><br/><br/>';
			}

			var editCouponLink = '#local.editCouponLink#&couponID=' + id;
			$('##divCouponFormArea').html(loadHTML).load(editCouponLink, function() { if (u == 1) location.href='##usage'; });
			$('##divCouponFormContainer').show();
		}		
		function deleteCoupon(id) {
			var result = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') listCouponsTable.draw();
				else {
					alert('We were unable to delete this coupon.');
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
				}
			};

			let delBtn = $('##btnDelCoupon'+id);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { couponID:id };
				TS_AJX('ADMINCOUPON','deleteCoupon',objParams,result,result,10000,result);
			});
		}
		function reactivateCoupon(id){
			var result = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') resetCouponsFilter();
				else {
					alert('We were unable to reactivate this coupon.');
				}
			};

			var objParams = { couponID:id };
			TS_AJX('ADMINCOUPON','reactivateCoupon',objParams,result,result,10000,result);
		}
		function cancelCouponForm() {
			var gridBox = $('##listCouponsTable');

			clearCouponFormContent();
			showCouponsGrid();

			if(gridBox.length && gridBox.is(':visible')) {
				$('html, body').animate({scrollTop: gridBox.offset().top - 450}, 750);
			}
			return false;
		}
		function clearCouponFormContent() {
			$('##divCouponFormArea').html('');
			$('##divCouponFormContainer').hide();
		}
		function showCouponsGrid() {
			$('##divCouponsGridContainer').show();
		}
		function reloadAndShowCouponsGrid(){
			clearCouponFormContent();
			listCouponsTable.draw();
			showCouponsGrid();
		}
		function initCouponsTable(){
			listCouponsTable = $('##listCouponsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50 ],
				"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.couponJSONLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmCouponsFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 	"data":null, 
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<span class="badge badge-neutral-primary text-primary">'+data.couponCode+'</span>'+ (data.status == 'I' ? '<span class="badge badge-warning ml-2">Inactive</span>' : '') ;
								renderData += '<div class="small py-1 pl-1">';
								if(data.pctOff > 0) {
									renderData += data.pctOff + '% off' + (data.pctOffMaxOff > 0 ? ' up to $' + parseFloat(data.pctOffMaxOff).toFixed(2).replaceAll('.00','') : '');
								} else if (data.amtOff > 0) {
									renderData += '$'+parseFloat(data.amtOff).toFixed(2).replaceAll('.00','');
								}
								if (data.maxMemberUsageCount > 0) {
									renderData += '; ' + data.maxMemberUsageCount + ' per member';
								}
								if (data.maxOverallUsageCount > 0) {
									renderData += '; ' + data.maxOverallUsageCount + ' max';
								}
								renderData += '</div>';
								if (data.usages.length) {
									renderData += '<div class="py-0 pl-1 small">';
									let arrUsages = data.usages.split(',');
									$.each(arrUsages, function(i,usage) {
										renderData += '<span class="mr-1">'+usage+'</span>';
									});
									renderData += '</div>';
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "35%",
						"className": "align-top"
					},
					{ 	"data":null,
						"orderable": false, 
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if (data.startDate.length && data.endDate.length) {
									renderData += data.startDate + ' to ' + data.endDate;
								} else if (data.startDate.length) {
									renderData += 'From ' + data.startDate;
								} else if (data.endDate.length) {
									renderData += 'To ' + data.endDate;
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "25%",
						"className": "align-top"
					},
					{ 	"data":null, 
						"orderable": false,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += data.redemptionCount + (data.maxOverallUsageCount > 0 ? ' of ' + data.maxOverallUsageCount : '');
								renderData += '<div class="pt-1 small">$' + parseFloat(data.savingsAmt).toFixed(2).replaceAll('.00','') + ' discounted</div>';
							}
							return type === 'display' ? renderData : data;
						},	
						"width": "25%",
						"className": "align-top"
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if(data.status == 'I'){
									<cfif arguments.event.getValue('mc_admintoolInfo.myRights.ManageCoupons')>
										renderData += '<a href="##" class="btn btn-xs  btn-outline-success text-success px-1 mx-1" title="Reactivate Coupon" onclick="reactivateCoupon('+data.couponID+');return false;"><i class="fa-solid fa-shuffle"></i></a>';
									</cfif>
								} else{	
									renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editCoupon('+data.couponID+',0);return false;" title="Edit Coupon"><i class="fa-solid <cfif arguments.event.getValue('mc_admintoolInfo.myRights.ManageCoupons')>fa-pencil<cfelse>fa-eye</cfif>"></i></a>';
									renderData += '<a href="##" class="btn btn-xs btn-outline-warning p-1 m-1" onclick="mca_showPermissions('+data.siteResourceID+',\'Coupon '+data.couponCode+'\');return false;" title="Manage Groups Using this Coupon"><i class="fa-solid fa-lock"></i></a>';
									<cfif arguments.event.getValue('mc_admintoolInfo.myRights.ManageCoupons')>
										renderData += '<a href="##" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="deleteCoupon('+data.couponID+');return false;" id="btnDelCoupon'+data.couponID+'" title="Delete Coupon"><i class="fa-solid fa-trash-can"></i></a>';
									</cfif>
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%",
						"className": "text-center align-top",
						"orderable": false
					}
				],
				"order": [[0, 'asc']],
				"searching": false
			});
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">

<cfoutput>
<div id="divCouponsGridContainer">
	<div class="toolButtonBar">
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.ManageCoupons')>
			<div><a href="javascript:editCoupon(0,0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new coupon."><i class="fa-regular fa-circle-plus"></i> Add Coupon</a></div>
		</cfif>
		<div><a href="javascript:showCouponFilter();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter coupons."><i class="fa-regular fa-filter"></i> Filter Coupons</a></div>
	</div>
	<div id="divCouponsFilterForm" class="coupontool" style="display:none;">
		<form name="frmCouponsFilter" id="frmCouponsFilter" onsubmit="filterCoupons();return false;">
			<div class="row my-3">
				<div class="col-xl-12">
					<div class="card card-box mb-1">
						<div class="card-header py-1 bg-light">
							<div class="card-header--title font-weight-bold font-size-md">
								Filter Coupons
							</div>
						</div>
						<div class="card-body pb-2">						
							<div class="row">
								<div class="col-xl-6 col-lg-12">
									<div class="form-row">
										<div class="col">
											<div class="form-label-group mb-2">
												<input type="text" name="fCouponCode" id="fCouponCode" class="form-control text-uppercase" value="" maxlength="15">
												<label for="fCouponCode">Coupon Code</label>
											</div>
										</div>
										<div class="col">
											<div class="form-label-group mb-2">
												<select name="fStatus" id="fStatus" class="form-control">
													<option value="A" selected>Active Coupons Only</option>
													<option value="I">Inactive Coupons Only</option>
													<option value="">Either Active or Inactive Coupons</option>
												</select>
												<label for="fStatus">Status</label>
											</div>
										</div>
									</div>
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="fUsage" id="fUsage" class="form-control">
												<option value="">Any Usage Area</option>
												<cfif this.strUsages.showEvents>
													<option value="ev">Events</option>
												</cfif>
												<cfif this.strUsages.showSemWeb>
													<option value="sw">SeminarWeb</option>
												</cfif>
												<cfif this.strUsages.showStore>
													<option value="sto">Store</option>
												</cfif>
												<cfif this.strUsages.showSubs>
													<option value="sub">Subscriptions</option>
												</cfif>
											</select>
											<label for="fUsage">Usage Area</label>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-lg-12">
									<div class="form-row">
										<div class="col">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="fAvailableFrom" id="fAvailableFrom" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fAvailableFrom"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fAvailableFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="fAvailableFrom">Available From</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="fAvailableTo" id="fAvailableTo" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fAvailableTo"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fAvailableTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="fAvailableTo">Available To</label>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="col">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="fCreatedFrom" id="fCreatedFrom" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fCreatedFrom"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fCreatedFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="fCreatedFrom">Created From</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="fCreatedTo" id="fCreatedTo" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fCreatedTo"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fCreatedTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="fCreatedTo">Created To</label>
													</div>
												</div>
											</div>	
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="card-footer p-2 text-right">
							<button type="button" name="btnClearFilter" class="btn btn-sm btn-secondary" onclick="resetCouponsFilter();">Clear Filters</button>
							&nbsp;
							<button type="submit" name="btnFilterCoupon" class="btn btn-sm btn-primary">
								<i class="fa-light fa-filter"></i> Filter Coupons
							</button> 							
						</div>
					</div>
				</div>
			</div>	
		</form>
	</div>
	<table id="listCouponsTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>Coupon Code</th>
				<th>Availablilty</th>
				<th>Redemptions</th>
				<th>Tools</th>
			</tr>
		</thead>
	</table>
</div>
<div id="divCouponFormContainer" style="display:none;">
	<div id="divCouponFormArea"></div>
</div>
</cfoutput>