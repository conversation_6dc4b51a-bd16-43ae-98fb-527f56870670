<cfsavecontent variable="local.overallJS">
<cfoutput>
	<script language="javascript">
	var #toScript(arguments.event.getValue('showBadge',''), 'showBadge')#;
	var #toScript(arguments.event.getValue('tab',''), 'jumpToSection')#;
	var #toScript(arguments.event.getValue('lastIncompleteSectionIndex',0), 'lastIncompleteSectionIndex')#;
	var drawCallbackExecuted = false;
	var onSellInCatalog = false;
	var strWellInitStatus = {};

	function isSectionInitialLoad(sectionID){
		return !strWellInitStatus.hasOwnProperty(sectionID) || !strWellInitStatus[sectionID];
	}
	function markSectionAsLoaded(sectionID){
		strWellInitStatus[sectionID] = true;
	}
	function getQueryParam(param) {
		var urlParams = new URLSearchParams(window.location.search);
		return urlParams.get(param);
	}
	var openTitleID = getQueryParam('openTitleID') || 0;
	function expandProgramTabSection(sectionId) {
		var section = $('##' + sectionId);
		var currentSection = $('.program-section.expanded');
		if (section.hasClass('expanded') && !programAdded) {
			closeProgramTabSection(sectionId);
		} else {
			openProgramTabSection(sectionId);
			autoScrollToSection(section);
			if(sectionId == 'program-basics')
				$('##prevButton').prop('disabled',true);
			else $('##prevButton').prop('disabled',false);
			}
		}
	function openProgramTabSection(sectionId) {
		$('.program-section').removeClass('expanded');
		$('.program-section .card-footer').addClass('d-none');

		var section = $('##' + sectionId);
		section.addClass('expanded');
		if(!programAdded)
			section.find('.card-footer').removeClass('d-none');
		<cfif !local.isPublisher>
			if(sectionId == 'program-basics')
				section.find('.card-footer').addClass('d-none');
		</cfif>
		if(sectionId == 'program-basics'){
			<cfif local.isPublisher>
				if(isSectionInitialLoad(sectionId)) initLearningObjectivesTable();
			</cfif>
		} else if(sectionId == 'program-catalog') {
			if(isSectionInitialLoad(sectionId)) initSWBCatalog();
			if(onSellInCatalog && programAdded){
				$('##allowCatalogOption').click();
				onSellInCatalog = false;
			}
		} else if(sectionId == 'program-seminars') {
			if(isSectionInitialLoad(sectionId)) {
				initSWBListItemsTable(); 
				<cfif local.qryBundle.isSWOD is 1>
					addSWODProgramToBundle();
				<cfelse>
					addSWLProgramToBundle();
				</cfif>
			}
			else SWBListItemsTable.draw();
		}
		else if(sectionId == 'program-syndication' && isSectionInitialLoad(sectionId)){
			initSWBSyndication();
		}

		markSectionAsLoaded(sectionId);
	}
	function closeProgramTabSection(sectionId) {
		var section = $('##' + sectionId);
		section.removeClass('expanded');
		section.find('.card-footer').addClass('d-none');
		if(!programAdded)
			$('html, body').animate({ scrollTop: 0 }, 500);
	}
	function autoScrollToSection(section) {
		$('html, body').animate({
			scrollTop: section.offset().top - 120
		}, 500);
	}
	function saveProgramTabSection(sectionId) {
		var section = $('##' + sectionId);
		function onSaveComplete() {
			var hasErrors = section.find('[id^="err"]').filter(function() {
				return !$(this).hasClass('d-none');
			}).length > 0;
			if(programAdded == true && !hasErrors)
				navigateProgramTab('next');
			else if(!programAdded)
				closeProgramTabSection(sectionId)
			else return;
		}
		if(programAdded == true)
			$('##nextButton').prop('disabled',true);
		else
			section.find('.card-footer .save-button').prop('disabled',true);
		if(sectionId == 'program-basics') {
			validateAndSaveSWBProgram(onSaveComplete);
		}
		else if(sectionId == 'program-settings') {
			validateAndSaveSWBProgramSettings(onSaveComplete);
		}
		else if(sectionId == 'program-catalog')
			saveSWBRateCatalog('',onSaveComplete,false);
		else if(sectionId == 'program-seminars' && !programAdded)
			validateIfSeminarsAddedToBundle(onSaveComplete);
		else if ((sectionId == 'program-syndication' && programAdded) || (sectionId == 'program-seminars' && programAdded)) {
			if(sectionId == 'program-syndication')
				saveSWBRateSyndication(onSaveComplete);
			else validateIfSeminarsAddedToBundle(onSaveComplete);
			if($('##err_syndication').hasClass('d-none') || $('##err_addprogram').hasClass('d-none')){
				navigateProgramTab('next');
				$('.progress').addClass('d-none');
				$('.program-section').addClass('d-none');
				$('##successMsg').removeClass('d-none');
				$('html, body').animate({ scrollTop: 0 }, 500);
				if(hasInactiveSeminarsIncluded)
					$('##divActivateProgram').addClass('d-none');
				else $('##divActivateProgram').removeClass('d-none');
				$('##nextButton').text('Launch Bundle');
				$('##prevButton').text('Go Back to Editing');
				$('##nextButton').prop('disabled',false);
				$('##nextButton').off('click');
				$('##prevButton').off('click');
				$('##nextButton').click(function() {
					$('##programAdded').val('false');
					var saveResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true'){
							self.location.href = link_editswbprogram;
						}
						else {
							alert('An error occured while submitting the webinar information.');
							$('##divSubmitProgramLoading').addClass('d-none');
							$('##nextButton,##prevButton').prop('disabled',false);
						}
					}

					$('##divSubmitProgramLoading').removeClass('d-none');
					$('##nextButton,##prevButton').prop('disabled',true);

					var objParams = getSWBBasicsFormData();
					objParams.sendConfirmationEmail = 1;
					if(!$('##divActivateProgram').hasClass('d-none')){
						objParams.bundleStatus = $('##activateProgram').prop('checked') ? 'A' : 'I';
					}
				
					TS_AJX('ADMINSWB','updateSWBProgram',objParams,saveResult,saveResult,20000,saveResult);
				});

				$('##prevButton').click(function() {
					$('.progress').removeClass('d-none');
					$('.program-section').removeClass('d-none');
					$('##successMsg').addClass('d-none');
					$('##prevButton').text('Go Back');
					$('##nextButton').text('Continue');
					openProgramTabSection('program-syndication');
					autoScrollToSection($('##program-syndication'));
					$('##prevButton').off('click');
					$('##nextButton').off('click');

					attachOriginalHandlers();
				});
			} else $('html,body').animate({scrollTop: $('##err_syndication').offset().top-120},500);
			return;
		}
		else if(sectionId == 'program-syndication')
			saveSWBRateSyndication(onSaveComplete);
		else onSaveComplete();
	}
	function attachOriginalHandlers() {
		$('##prevButton').click(function() {
			navigateProgramTab('prev');
		});

		$('##nextButton').click(function() {
			saveProgramTabSection($('.program-section.expanded').attr('id'));
		});
	}
	function displaySavedResponse(section){
		if (!section.find('.card-header ##saveResponse').length) 
			section.find('.card-header--title').after('<span id="saveResponse"></span>');
		section.find('##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
	}
	<cfif local.programAdded>
		function getSectionIds() {
			var sectionIds = [];
			$('.program-section').each(function() {
				sectionIds.push(this.id);
			});
			return sectionIds;
		}
		function navigateProgramTab(direction) {
			var currentSection = $('.program-section.expanded');
			var nextSection;

			if (direction === 'next') {
				nextSection = currentSection.next('.program-section');
				if ($.inArray(currentSection.attr('id'), ['program-speakers','program-materials']) == -1) {
					displaySavedResponse(currentSection);
				}
				var sectionIds = getSectionIds();
				var currentIndex = sectionIds.indexOf(currentSection.attr('id'));
				if (currentIndex >= lastIncompleteSectionIndex) {
					lastIncompleteSectionIndex = currentIndex + 1;
				}
			} else if (direction === 'prev') {
				nextSection = currentSection.prev('.program-section');
			}
			var sectionName = nextSection.attr('id');
			if(sectionName == 'program-basics')
				$('##prevButton').prop('disabled', true);
			if (nextSection.length > 0 || (currentSection.attr('id') == 'program-recordings' && direction !== 'next')) {
				closeProgramTabSection(currentSection.attr('id'));
				if($('##' + sectionName).hasClass('d-none')){
					if(sectionName == 'program-catalog')
						onSellInCatalog = true;
					$('##' + sectionName).removeClass('d-none');
				}
				autoScrollToSection($('##' + sectionName));
				openProgramTabSection(nextSection.attr('id'));
			}
			if(direction == 'next')
				updateProgressBar();
			$('##nextButton').prop('disabled',false);
		}
		function updateProgressBar() {
			// Assuming you have a progress bar element in your HTML
			var progressBarTop = $('##progressBarTop');
			var progressBarBottom = $('##progressBarBottom');
			// Assuming you have a list of section IDs in order
			var sectionIds = getSectionIds();
			// Calculate progress percentage based on current section
			var totalSections = sectionIds.length;
			var progressPercent = ((lastIncompleteSectionIndex) / totalSections) * 100;
			// Update the progress bar width
			progressBarBottom.css('width', progressPercent + '%');
			progressBarBottom.attr('aria-valuenow', progressPercent);
			progressBarBottom.text(progressPercent.toFixed(0) + '% Completed');
			progressBarBottom.css('color', 'white');
			progressBarTop.css('width', progressPercent + '%');
			progressBarTop.attr('aria-valuenow', progressPercent);
			progressBarTop.text(progressPercent.toFixed(0) + '% Completed');
			progressBarTop.css('color', 'white');
			
		}
	</cfif>
	function initProgramTab() {
		// Check if programAdded is 1 for sequential card display
		if (programAdded == true) {
			$('.program-section .card-footer').addClass('d-none');
			$('.program-section').addClass('d-none');
			$('##program-basics').removeClass('d-none').addClass('expanded');
			$('##prevButton').prop('disabled', true);
			attachOriginalHandlers();
			var progressBarTop = $('##progressBarTop');
			var progressBarBottom = $('##progressBarBottom');
			progressBarBottom.css('width', '0%');
			progressBarBottom.attr('aria-valuenow', 0);
			progressBarBottom.text('0% Completed');
			progressBarBottom.css('color', 'grey');

			progressBarTop.css('width', '0%');
			progressBarTop.attr('aria-valuenow', 0);
			progressBarTop.text('0% Completed');
			progressBarTop.css('color', 'grey');
			
			$('.program-section').each(function(index) {
				if (index <= lastIncompleteSectionIndex) {
					$(this).removeClass('d-none');
				}
			});
			if(jumpToSection !== '' && $('##program-' + jumpToSection).length) {
				openProgramTabSection('program-' + jumpToSection);
				autoScrollToSection($('##program-' + jumpToSection));

				if(showBadge !== '' && $('##program-' + showBadge).length) {
					displaySavedResponse($('##program-' + showBadge));
					if(showBadge == 'catalog' || showBadge == 'credit'){
						updateProgressBar();
						$('##prevButton').prop('disabled', false);
					}
					else
						navigateProgramTab('next')
				}
			} else if(showBadge !== '' && $('##program-' + showBadge).length) {
				displaySavedResponse($('##program-' + showBadge));
				if(showBadge == 'catalog' || showBadge == 'credit')
					updateProgressBar();
				else if(showBadge == 'basics') {
					$('##prevButton').prop('disabled', false);
					navigateProgramTab('next');
				} else
					navigateProgramTab('next')
			} else {
				openProgramTabSection('program-basics');
			}
		} else {
			$('.program-section .card-footer').addClass('d-none');
			if(jumpToSection !== '' && $('##program-' + jumpToSection).length) {
				openProgramTabSection('program-' + jumpToSection);
				autoScrollToSection($('##program-' + jumpToSection));

				if(showBadge !== '' && $('##program-' + showBadge).length) {
					displaySavedResponse($('##program-' + showBadge));
				}
			} else if(showBadge !== '' && $('##program-' + showBadge).length) {
				displaySavedResponse($('##program-' + showBadge));
			} else {
				openProgramTabSection('program-basics');
			}

			$('.program-section').each(function() {
				var sectionId = $(this).attr('id');
				if(sectionId !== 'program-seminars') {
					var saveButton = $('<button class="btn btn-sm btn-primary save-button" <cfif local.qryBundle.lockSettings is 1>disabled</cfif>>Save</button>');
					saveButton.click(function() {
						saveProgramTabSection(sectionId);
						});
					$(this).find('.card-footer').append(saveButton);
				}
			});
		}
	}
	</script>
	<style>
		##program-tab .card {
			margin-bottom: 10px;
			border-radius: 10px; 
			overflow: hidden; 
		}
		##program-tab .card-header {
			cursor: pointer;
			border-top-left-radius: 10px; 
			border-top-right-radius: 10px; 
		}
		##program-tab .card-footer {
			text-align: right;
			border-bottom-left-radius: 10px; 
			border-bottom-right-radius: 10px; 
		}
		##program-tab .card-footer .save-button {
			margin-top: 10px;
		}
		##program-tab > .card > .card-header:after {
			content: '\25B8'; /* Unicode character for right-pointing triangle */
			float: right;
			font-size: 20px;
			transition: all 0.3s ease;
		}

		/* Change the arrow to down-pointing when expanded */
		##program-tab > .card.expanded > .card-header:after {
			content: '\25BE'; /* Unicode character for down-pointing triangle */
		}

		/* Prevent arrow icon from showing inside nested card headers */
		##program-tab .card-body .card-header:after {
			content: none;
		}
		##program-tab .card-body {
			display: none;
		}
		##program-tab .program-section.expanded .card-body {
			display: block;
		}
	</style>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.overallJS#">
<cfoutput>
	<div id="program-tab" class="tab-pane fade show active">
		<div id="program-basics" class="program-section card card-box">
			<div class="card-header bg-light" onclick="expandProgramTabSection('program-basics')">
				<div class="card-header--title">
					<b>Basics</b><div class="text-dark">Configure bundle status, titles, description, and learning objectives.</div>
				</div>
			</div>
			<div class="card-body">
				<cfif local.hasEditRights>
					<cfinclude template="frm_SWB_program_details.cfm">
				<cfelse>
					<cfinclude template="frm_SWB_program_summary.cfm">
				</cfif>
			</div>
			<div class="card-footer"></div>
		</div>

		<cfif local.hasEditRights>
			<div id="program-settings" class="program-section card card-box">
				<div class="card-header bg-light" onclick="expandProgramTabSection('program-settings')">
					<div class="card-header--title">
						<b>Settings</b><div class="text-dark">Customize bundle settings to guide registrants through purchase, registration, participation, and completion.</div>
					</div>
				</div>
				<div class="card-body">
					<cfinclude template="frm_SWB_program_settings.cfm">
				</div>
				<div class="card-footer"></div>
			</div>
		</cfif>

		<cfif local.hasManageSWBRatesRights>
			<div id="program-catalog" class="program-section card card-box">
				<div class="card-header bg-light" onclick="expandProgramTabSection('program-catalog')">
					<div class="card-header--title">
						<b>Catalog Details</b><div class="text-dark">Configure bundle sell dates, rates, qualifying groups, and catalog settings.</div>
					</div>
				</div>
				<div class="card-body">
					<cfinclude template="frm_SWB_program_catalog.cfm">
				</div>
				<div class="card-footer"></div>
			</div>

			<div id="program-seminars" class="program-section card card-box">
				<div class="card-header bg-light" onclick="expandProgramTabSection('program-seminars')">
					<div class="card-header--title">
						<b>Included <cfif local.qryBundle.isSWOD is 1>OnDemand Seminars<cfelse>Live Webinars</cfif></b><div class="text-dark">Select programs to include. Minimum required.</div>
					</div>
				</div>
				<div class="card-body">
					<cfinclude template="frm_SWB_program_items.cfm">
				</div>
			</div>
		</cfif>
		<cfif local.hasEditRights AND local.qryAssociation.handlesOwnPayment is 0>
			<div id="program-syndication" class="program-section card card-box">
				<div class="card-header bg-light" onclick="expandProgramTabSection('program-syndication')">
					<div class="card-header--title">
						<b>Syndication</b><div class="text-dark">Share bundle to partner associations. Contract agreement required.</div>
					</div>
				</div>
				<div class="card-body">
					<cfinclude template="frm_SWB_program_syndication.cfm">
				</div>
				<div class="card-footer"></div>
			</div>
		</cfif>
		
		<cfif NOT local.programAdded>
			<div class="form-row">
				<div class="col">
					<div class="form-group">
						<div class="form-label-group">
							<div class="input-group input-group">
								<input type="text" name="justview1" id="justview1" value="/?pg=semwebcatalog&panel=showBundle&bundleid=#local.qryBundle.bundleID#" class="form-control" readonly="readonly" onclick="this.select();"/>
								<div class="input-group-append">
									<span class="input-group-text"><a target="_blank" href="/?pg=semwebcatalog&panel=showBundle&bundleid=#local.qryBundle.bundleID#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
								</div>
								<label for="justview1">Internal Program URL</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="form-row">
				<div class="col">
					<div class="form-group">
						<div class="form-label-group">
							<div class="input-group input-group">
								<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=semwebcatalog&panel=showBundle&bundleid=#local.qryBundle.bundleID#" class="form-control" readonly="readonly" onclick="this.select();"/>
								<div class="input-group-append">
									<span class="input-group-text"><a target="_blank" href="/?pg=semwebcatalog&panel=showBundle&bundleid=#local.qryBundle.bundleID#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
								</div>
								<label for="justview2">External Program URL</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</cfif>
	</div>
	<div id="successMsg"class="alert alert-success mb-2 mt-2 d-none">
		<div class="alert-success d-flex align-items-center align-content-center my-2 " role="alert">
			<span class="font-size-lg d-block d-40 text-center">
				<i class="fa-regular fa-circle-check"></i>
			</span>
			<span>
				Your <cfif local.qryBundle.isSWOD is 1>OnDemand<cfelse>Live</cfif> bundle is ready to launch.
			</span>
		</div>
		<div class="custom-control custom-switch mb-2 ml-2" id="divActivateProgram">
			<input type="checkbox" name="activateProgram" id="activateProgram"  class="custom-control-input" <cfif local.qryBundle.status is 1> checked="checked" disabled</cfif>>
			<label class="custom-control-label text-body" for="activateProgram">
				Activate Bundle after launch. <i class="fa-solid fa-info-circle pl-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="" <cfif local.qryBundle.status is not 1>data-original-title="Bundle available for sale in catalog and accessible to registrants."<cfelse>data-original-title="The bundle is already activated."</cfif>></i>
			</label>
		</div>
		<div id="divSubmitProgramLoading" class="mt-2 d-none">
			<div class="mt-1 d-flex align-items-center ml-3">
				<div class="font-weight-bold">Please wait while we launch the <cfif local.qryBundle.isSWOD is 1>OnDemand<cfelse>Live</cfif> Bundle.</div>
				<div class="spinner-border spinner-border-sm ml-2" role="status"></div>
			</div>
		</div>
	</div>
	<cfif local.programAdded>
		<!-- Navigation buttons and progress bar -->
		<div id="program-navigation">
			<button id="prevButton" class="btn btn-sm btn-secondary">Go Back</button>
			<button id="nextButton" class="btn btn-sm btn-success float-right">Continue</button>
			<!-- Progress bar HTML structure -->
			<div class="progress mt-3" style="height: 20px;">
				<div id="progressBarBottom" class="progress-bar bg-success" role="progressbar" style="text-indent: 10px; width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
			</div>
		</div>
	</cfif>
</cfoutput>